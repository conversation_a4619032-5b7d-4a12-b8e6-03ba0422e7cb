<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能客服系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .chat-test {
            border: 1px solid #ddd;
            height: 200px;
            overflow-y: auto;
            padding: 10px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 5px;
            border-radius: 4px;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        .bot-message {
            background-color: #e9ecef;
            color: #333;
        }
        input[type="text"] {
            width: 70%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智能客服系统测试页面</h1>
        
        <!-- API测试 -->
        <div class="test-section">
            <div class="test-title">1. API接口测试</div>
            <button onclick="testAPIs()">测试所有API</button>
            <div id="api-results"></div>
        </div>

        <!-- WebSocket测试 -->
        <div class="test-section">
            <div class="test-title">2. WebSocket连接测试</div>
            <button onclick="connectWebSocket()">连接WebSocket</button>
            <button onclick="disconnectWebSocket()">断开连接</button>
            <div id="ws-status" class="test-result info">未连接</div>
        </div>

        <!-- 聊天测试 -->
        <div class="test-section">
            <div class="test-title">3. 聊天功能测试</div>
            <div id="chat-messages" class="chat-test"></div>
            <input type="text" id="message-input" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()">发送</button>
            <div id="chat-status"></div>
        </div>

        <!-- 系统状态 -->
        <div class="test-section">
            <div class="test-title">4. 系统状态</div>
            <div id="system-status">
                <p><strong>前端服务:</strong> <span id="frontend-status">检测中...</span></p>
                <p><strong>后端服务:</strong> <span id="backend-status">检测中...</span></p>
                <p><strong>WebSocket:</strong> <span id="websocket-status">未连接</span></p>
            </div>
        </div>
    </div>

    <script>
        let websocket = null;
        let currentSessionId = null;

        // 页面加载时检查系统状态
        window.onload = function() {
            checkSystemStatus();
        };

        // 检查系统状态
        async function checkSystemStatus() {
            // 检查前端
            try {
                const response = await fetch('/');
                document.getElementById('frontend-status').textContent = '✅ 正常运行';
                document.getElementById('frontend-status').style.color = 'green';
            } catch (error) {
                document.getElementById('frontend-status').textContent = '❌ 连接失败';
                document.getElementById('frontend-status').style.color = 'red';
            }

            // 检查后端
            try {
                const response = await fetch('/dev-api/chatbot/online/count');
                const data = await response.json();
                if (data.code === 200) {
                    document.getElementById('backend-status').textContent = '✅ 正常运行';
                    document.getElementById('backend-status').style.color = 'green';
                } else {
                    throw new Error('API返回错误');
                }
            } catch (error) {
                document.getElementById('backend-status').textContent = '❌ 连接失败';
                document.getElementById('backend-status').style.color = 'red';
            }
        }

        // 测试所有API
        async function testAPIs() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<div class="test-result info">正在测试API...</div>';
            
            const tests = [
                { name: '获取在线用户数', url: '/dev-api/chatbot/online/count' },
                { name: '获取活跃会话', url: '/dev-api/chatbot/session/active' },
                { name: '创建会话', url: '/dev-api/chatbot/session/create', method: 'POST' }
            ];

            let results = [];
            
            for (const test of tests) {
                try {
                    const options = {
                        method: test.method || 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    };
                    
                    const response = await fetch(test.url, options);
                    const data = await response.json();
                    
                    if (data.code === 200) {
                        results.push(`✅ ${test.name}: 成功`);
                        if (test.name === '创建会话') {
                            currentSessionId = data.data;
                        }
                    } else {
                        results.push(`❌ ${test.name}: ${data.msg}`);
                    }
                } catch (error) {
                    results.push(`❌ ${test.name}: ${error.message}`);
                }
            }
            
            resultsDiv.innerHTML = `<div class="test-result ${results.every(r => r.startsWith('✅')) ? 'success' : 'error'}">${results.join('<br>')}</div>`;
        }

        // 连接WebSocket
        function connectWebSocket() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                updateWSStatus('已经连接', 'success');
                return;
            }

            const userId = '1';
            const wsUrl = `ws://localhost:8090/websocket/chat/${userId}`;
            
            websocket = new WebSocket(wsUrl);
            
            websocket.onopen = function() {
                updateWSStatus('✅ 连接成功', 'success');
                document.getElementById('websocket-status').textContent = '✅ 已连接';
                document.getElementById('websocket-status').style.color = 'green';
            };
            
            websocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                addMessage(data.content, 'bot-message');
            };
            
            websocket.onclose = function() {
                updateWSStatus('❌ 连接关闭', 'error');
                document.getElementById('websocket-status').textContent = '❌ 未连接';
                document.getElementById('websocket-status').style.color = 'red';
            };
            
            websocket.onerror = function(error) {
                updateWSStatus('❌ 连接错误: ' + error, 'error');
                document.getElementById('websocket-status').textContent = '❌ 连接错误';
                document.getElementById('websocket-status').style.color = 'red';
            };
        }

        // 断开WebSocket
        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
                updateWSStatus('连接已断开', 'info');
            }
        }

        // 更新WebSocket状态
        function updateWSStatus(message, type) {
            const statusDiv = document.getElementById('ws-status');
            statusDiv.textContent = message;
            statusDiv.className = `test-result ${type}`;
        }

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (!message) return;
            
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                alert('请先连接WebSocket');
                return;
            }
            
            if (!currentSessionId) {
                alert('请先创建会话（点击测试所有API）');
                return;
            }
            
            // 显示用户消息
            addMessage(message, 'user-message');
            
            // 发送到服务器
            const data = {
                sessionId: currentSessionId,
                content: message,
                messageType: '1'
            };
            
            websocket.send(JSON.stringify(data));
            input.value = '';
        }

        // 处理回车键
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 添加消息到聊天区域
        function addMessage(content, className) {
            const chatDiv = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${className}`;
            messageDiv.textContent = content;
            chatDiv.appendChild(messageDiv);
            chatDiv.scrollTop = chatDiv.scrollHeight;
        }
    </script>
</body>
</html>
