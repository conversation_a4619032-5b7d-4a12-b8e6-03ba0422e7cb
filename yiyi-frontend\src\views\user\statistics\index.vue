<template>
  <div class="app-container">
    <!-- 统计概览 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon total">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ overviewData.totalUsers || 0 }}</div>
              <div class="stats-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon today">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ overviewData.todayNewUsers || 0 }}</div>
              <div class="stats-label">今日新增</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon active">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ overviewData.activeUsers || 0 }}</div>
              <div class="stats-label">活跃用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon growth">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ overviewData.growthRate || 0 }}%</div>
              <div class="stats-label">增长率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 用户增长趋势 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>用户增长趋势</span>
              <el-radio-group v-model="growthPeriod" @change="loadGrowthData">
                <el-radio-button value="7">近7天</el-radio-button>
                <el-radio-button value="30">近30天</el-radio-button>
                <el-radio-button value="90">近90天</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="growthChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <!-- 用户等级分布 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户等级分布</span>
          </template>
          <div ref="levelChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 地域分布和活跃度分析 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 用户地域分布 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户地域分布</span>
          </template>
          <div ref="regionChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>
      
      <!-- 用户活跃度分析 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>用户活跃度分析</span>
              <el-button text @click="loadActivityData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div ref="activityChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <span>用户统计详情</span>
      </template>
      
      <el-table :data="statisticsData" v-loading="tableLoading">
        <el-table-column label="日期" prop="date" width="120" />
        <el-table-column label="新增用户" prop="newUsers" width="100" />
        <el-table-column label="活跃用户" prop="activeUsers" width="100" />
        <el-table-column label="普通用户" prop="normalUsers" width="100" />
        <el-table-column label="VIP用户" prop="vipUsers" width="100" />
        <el-table-column label="SVIP用户" prop="svipUsers" width="100" />
        <el-table-column label="男性用户" prop="maleUsers" width="100" />
        <el-table-column label="女性用户" prop="femaleUsers" width="100" />
        <el-table-column label="总对话次数" prop="totalChats" width="120" />
        <el-table-column label="平均对话时长" prop="avgChatDuration" width="120" />
      </el-table>
      
      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="loadStatisticsData"
      />
    </el-card>
  </div>
</template>

<script setup name="UserStatistics">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { User, UserFilled, Connection, TrendCharts, Refresh } from '@element-plus/icons-vue'
import { getUserStatistics, getUserDistribution } from '@/api/chatbot/user.js'
import { getUserGrowthData, getUserActivityData } from '@/api/chatbot/dashboard.js'
import * as echarts from 'echarts'
import Pagination from '@/components/Pagination/index.vue'

// 响应式数据
const loading = ref(false)
const tableLoading = ref(false)
const overviewData = reactive({
  totalUsers: 0,
  todayNewUsers: 0,
  activeUsers: 0,
  growthRate: 0
})
const statisticsData = ref([])
const total = ref(0)
const growthPeriod = ref('30')

// 图表引用
const growthChartRef = ref()
const levelChartRef = ref()
const regionChartRef = ref()
const activityChartRef = ref()

// 图表实例
let growthChart = null
let levelChart = null
let regionChart = null
let activityChart = null

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
})

// 初始化
onMounted(() => {
  loadOverviewData()
  loadStatisticsData()
  nextTick(() => {
    initCharts()
  })
})

// 清理
onUnmounted(() => {
  if (growthChart) growthChart.dispose()
  if (levelChart) levelChart.dispose()
  if (regionChart) regionChart.dispose()
  if (activityChart) activityChart.dispose()
})

// 加载概览数据
const loadOverviewData = async () => {
  try {
    const response = await getUserStatistics({ type: 'overview' })
    Object.assign(overviewData, response.data)
  } catch (error) {
    console.error('获取概览数据失败:', error)
  }
}

// 加载统计数据
const loadStatisticsData = async () => {
  try {
    tableLoading.value = true
    const response = await getUserStatistics({
      type: 'detail',
      ...queryParams
    })
    statisticsData.value = response.rows || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取统计数据失败:', error)
  } finally {
    tableLoading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  initGrowthChart()
  initLevelChart()
  initRegionChart()
  initActivityChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 初始化增长趋势图表
const initGrowthChart = async () => {
  if (!growthChartRef.value) return
  
  growthChart = echarts.init(growthChartRef.value)
  await loadGrowthData()
}

// 加载增长数据
const loadGrowthData = async () => {
  try {
    const response = await getUserGrowthData({ period: growthPeriod.value })
    const data = response.data || []
    
    const option = {
      title: {
        text: '用户增长趋势',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['新增用户', '累计用户'],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.date)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '新增用户',
          type: 'bar',
          data: data.map(item => item.newUsers),
          itemStyle: { color: '#409eff' }
        },
        {
          name: '累计用户',
          type: 'line',
          data: data.map(item => item.totalUsers),
          itemStyle: { color: '#67c23a' }
        }
      ]
    }
    
    growthChart.setOption(option)
  } catch (error) {
    console.error('加载增长数据失败:', error)
  }
}

// 初始化等级分布图表
const initLevelChart = async () => {
  if (!levelChartRef.value) return
  
  levelChart = echarts.init(levelChartRef.value)
  
  try {
    const response = await getUserStatistics({ type: 'level' })
    const data = response.data || []
    
    const option = {
      title: {
        text: '用户等级分布',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '用户等级',
          type: 'pie',
          radius: '50%',
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    
    levelChart.setOption(option)
  } catch (error) {
    console.error('加载等级分布数据失败:', error)
  }
}

// 初始化地域分布图表
const initRegionChart = async () => {
  if (!regionChartRef.value) return
  
  regionChart = echarts.init(regionChartRef.value)
  
  try {
    const response = await getUserDistribution({ type: 'region' })
    const data = response.data || []
    
    const option = {
      title: {
        text: '用户地域分布',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '用户数量',
          type: 'map',
          map: 'china',
          roam: false,
          data: data
        }
      ]
    }
    
    regionChart.setOption(option)
  } catch (error) {
    console.error('加载地域分布数据失败:', error)
  }
}

// 初始化活跃度图表
const initActivityChart = async () => {
  if (!activityChartRef.value) return
  
  activityChart = echarts.init(activityChartRef.value)
  await loadActivityData()
}

// 加载活跃度数据
const loadActivityData = async () => {
  try {
    const response = await getUserActivityData({ period: 7 })
    const data = response.data || []
    
    const option = {
      title: {
        text: '用户活跃度分析',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.hour + ':00')
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '活跃用户数',
          type: 'line',
          data: data.map(item => item.activeUsers),
          smooth: true,
          itemStyle: { color: '#e6a23c' }
        }
      ]
    }
    
    activityChart.setOption(option)
  } catch (error) {
    console.error('加载活跃度数据失败:', error)
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (growthChart) growthChart.resize()
  if (levelChart) levelChart.resize()
  if (regionChart) regionChart.resize()
  if (activityChart) activityChart.resize()
}
</script>

<style scoped>
.stats-card {
  height: 120px;
}

.stats-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.today {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.growth {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #666;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
