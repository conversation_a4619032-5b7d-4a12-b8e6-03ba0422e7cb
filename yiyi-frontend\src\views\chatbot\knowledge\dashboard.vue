<template>
  <div class="app-container">
    <!-- 统计概览 -->
    <KnowledgeStats ref="statsRef" />

    <!-- 快速操作 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>快速操作</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="quick-action" @click="$router.push('/chatbot/knowledge')">
            <el-icon class="action-icon"><Plus /></el-icon>
            <div class="action-text">新增知识库</div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="quick-action" @click="$router.push('/chatbot/knowledge/category')">
            <el-icon class="action-icon"><FolderAdd /></el-icon>
            <div class="action-text">管理分类</div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="quick-action" @click="$router.push('/chatbot/knowledge/search')">
            <el-icon class="action-icon"><Search /></el-icon>
            <div class="action-text">搜索知识库</div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="quick-action" @click="handleImport">
            <el-icon class="action-icon"><Upload /></el-icon>
            <div class="action-text">批量导入</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据概览卡片 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近更新</span>
              <div>
                <el-button text @click="loadRecentKnowledge">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
                <el-button type="primary" text @click="$router.push('/chatbot/knowledge')">
                  查看全部
                </el-button>
              </div>
            </div>
          </template>

          <div class="recent-list" v-loading="loading">
            <div
              v-for="item in recentKnowledge.slice(0, 5)"
              :key="item.knowledgeId"
              class="recent-item"
              @click="viewKnowledge(item)"
            >
              <div class="recent-content">
                <div class="recent-title">{{ item.title }}</div>
                <div class="recent-meta">
                  <el-tag size="small" type="info">{{ item.categoryName }}</el-tag>
                  <span class="recent-time">{{ formatRelativeTime(item.createTime) }}</span>
                </div>
              </div>
              <div class="recent-actions">
                <el-button type="primary" text size="small" @click.stop="editKnowledge(item)">
                  编辑
                </el-button>
              </div>
            </div>
            <el-empty v-if="recentKnowledge.length === 0" description="暂无数据" :image-size="60" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>待处理事项</span>
              <el-badge :value="todoCount" class="todo-badge">
                <el-icon><Bell /></el-icon>
              </el-badge>
            </div>
          </template>

          <div class="todo-list">
            <div class="todo-item" v-if="stats.inactive > 0">
              <div class="todo-icon warning">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="todo-content">
                <div class="todo-title">停用的知识库</div>
                <div class="todo-desc">{{ stats.inactive }} 个知识库处于停用状态</div>
              </div>
              <el-button type="warning" text @click="handleInactiveKnowledge">
                处理
              </el-button>
            </div>

            <div class="todo-item" v-if="stats.noCategory > 0">
              <div class="todo-icon danger">
                <el-icon><FolderRemove /></el-icon>
              </div>
              <div class="todo-content">
                <div class="todo-title">未分类知识库</div>
                <div class="todo-desc">{{ stats.noCategory }} 个知识库未设置分类</div>
              </div>
              <el-button type="danger" text @click="handleNoCategoryKnowledge">
                处理
              </el-button>
            </div>

            <div class="todo-item" v-if="stats.lowHit > 0">
              <div class="todo-icon info">
                <el-icon><DataLine /></el-icon>
              </div>
              <div class="todo-content">
                <div class="todo-title">低命中知识库</div>
                <div class="todo-desc">{{ stats.lowHit }} 个知识库命中次数较低</div>
              </div>
              <el-button type="info" text @click="handleLowHitKnowledge">
                优化
              </el-button>
            </div>

            <div v-if="todoCount === 0" class="no-todo">
              <el-icon><CircleCheck /></el-icon>
              <span>暂无待处理事项</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分类统计图表 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>分类统计</span>
        </div>
      </template>
      
      <div ref="chartRef" style="height: 300px;"></div>
    </el-card>

    <!-- 导入组件 -->
    <KnowledgeImport ref="importRef" @success="handleImportSuccess" />
  </div>
</template>

<script setup name="KnowledgeDashboard">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, FolderAdd, Search, Upload, Refresh, Bell, Warning, FolderRemove, DataLine, CircleCheck } from '@element-plus/icons-vue'
import { listKnowledge, getCategoryTree } from '@/api/chatbot/knowledge.js'
import { parseTime } from '@/utils/ruoyi'
import KnowledgeStats from '@/components/KnowledgeStats/index.vue'
import KnowledgeImport from '@/components/KnowledgeImport/index.vue'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const recentKnowledge = ref([])
const statsRef = ref()
const importRef = ref()
const chartRef = ref()
let chartInstance = null

// 新增统计数据
const stats = reactive({
  total: 0,
  active: 0,
  inactive: 0,
  categories: 0,
  totalHits: 0,
  noCategory: 0,
  lowHit: 0
})

// 计算待处理事项数量
const todoCount = computed(() => {
  return stats.inactive + stats.noCategory + stats.lowHit
})

// 初始化
onMounted(() => {
  loadRecentKnowledge()
  loadExtendedStats()
  nextTick(() => {
    initChart()
  })
})

// 加载最近更新的知识库
const loadRecentKnowledge = async () => {
  try {
    loading.value = true
    const response = await listKnowledge({
      pageNum: 1,
      pageSize: 10,
      orderByColumn: 'createTime',
      isAsc: 'desc'
    })
    recentKnowledge.value = response.rows || []
  } catch (error) {
    console.error('加载最近知识库失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载扩展统计数据
const loadExtendedStats = async () => {
  try {
    // 获取停用的知识库数量
    const inactiveResponse = await listKnowledge({ status: '0', pageNum: 1, pageSize: 1 })
    stats.inactive = inactiveResponse.total || 0

    // 获取未分类的知识库数量（假设categoryId为null或0表示未分类）
    const noCategoryResponse = await listKnowledge({ categoryId: 0, pageNum: 1, pageSize: 1 })
    stats.noCategory = noCategoryResponse.total || 0

    // 获取低命中的知识库数量（命中次数小于5）
    const allResponse = await listKnowledge({ pageNum: 1, pageSize: 1000 })
    const allKnowledge = allResponse.rows || []
    stats.lowHit = allKnowledge.filter(item => (item.hitCount || 0) < 5).length

  } catch (error) {
    console.error('加载扩展统计失败:', error)
  }
}

// 格式化相对时间
const formatRelativeTime = (time) => {
  const now = new Date()
  const target = new Date(time)
  const diff = now - target
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  if (days < 30) return `${Math.floor(days / 7)}周前`
  return parseTime(time, '{m}-{d}')
}

// 处理停用知识库
const handleInactiveKnowledge = () => {
  window.open('/chatbot/knowledge?status=0', '_blank')
}

// 处理未分类知识库
const handleNoCategoryKnowledge = () => {
  window.open('/chatbot/knowledge?categoryId=0', '_blank')
}

// 处理低命中知识库
const handleLowHitKnowledge = () => {
  window.open('/chatbot/knowledge?hitCount=low', '_blank')
}

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return
  
  try {
    // 获取分类统计数据
    const categoryResponse = await getCategoryTree()
    const categories = categoryResponse.data || []
    
    // 统计每个分类的知识库数量
    const categoryStats = await Promise.all(
      categories.map(async (category) => {
        const response = await listKnowledge({
          categoryId: category.categoryId,
          pageNum: 1,
          pageSize: 1
        })
        return {
          name: category.categoryName,
          value: response.total || 0
        }
      })
    )

    // 初始化图表
    chartInstance = echarts.init(chartRef.value)
    
    const option = {
      title: {
        text: '知识库分类分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '知识库数量',
          type: 'pie',
          radius: '50%',
          data: categoryStats,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    
    chartInstance.setOption(option)
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      chartInstance?.resize()
    })
    
  } catch (error) {
    console.error('初始化图表失败:', error)
  }
}

// 编辑知识库
const editKnowledge = (row) => {
  // 跳转到知识库管理页面并传递ID
  window.open(`/chatbot/knowledge?edit=${row.knowledgeId}`, '_blank')
}

// 查看知识库
const viewKnowledge = (row) => {
  // 跳转到知识库搜索页面
  window.open(`/chatbot/knowledge/search?id=${row.knowledgeId}`, '_blank')
}

// 处理导入
const handleImport = () => {
  importRef.value?.handleImport()
}

// 导入成功回调
const handleImportSuccess = () => {
  loadRecentKnowledge()
  statsRef.value?.refresh()
  ElMessage.success('导入成功，数据已刷新')
}

// 组件销毁时清理图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    window.removeEventListener('resize', () => {
      chartInstance?.resize()
    })
  }
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.quick-action:hover {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.action-icon {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 8px;
}

.action-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 最近更新样式 */
.recent-list {
  max-height: 300px;
  overflow-y: auto;
}

.recent-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.recent-item:hover {
  background-color: #f8f9fa;
  margin: 0 -12px;
  padding: 12px;
  border-radius: 6px;
}

.recent-item:last-child {
  border-bottom: none;
}

.recent-content {
  flex: 1;
}

.recent-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  font-size: 14px;
}

.recent-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recent-time {
  font-size: 12px;
  color: #999;
}

.recent-actions {
  opacity: 0;
  transition: opacity 0.3s;
}

.recent-item:hover .recent-actions {
  opacity: 1;
}

/* 待处理事项样式 */
.todo-badge {
  cursor: pointer;
}

.todo-list {
  max-height: 300px;
  overflow-y: auto;
}

.todo-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
  color: white;
}

.todo-icon.warning {
  background-color: #e6a23c;
}

.todo-icon.danger {
  background-color: #f56c6c;
}

.todo-icon.info {
  background-color: #409eff;
}

.todo-content {
  flex: 1;
}

.todo-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
  font-size: 14px;
}

.todo-desc {
  font-size: 12px;
  color: #666;
}

.no-todo {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
  font-size: 14px;
}

.no-todo .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #67c23a;
}
</style>
