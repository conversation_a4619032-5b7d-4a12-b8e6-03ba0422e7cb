<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <div>
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete"
            >
              批量删除
            </el-button>
            <el-button
              type="warning"
              plain
              icon="Download"
              @click="handleExport"
            >
              导出数据
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="昵称" prop="nickname">
          <el-input
            v-model="queryParams.nickname"
            placeholder="请输入用户昵称"
            clearable
            @keyup.enter="handleQuery"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phoneNumber">
          <el-input
            v-model="queryParams.phoneNumber"
            placeholder="请输入手机号"
            clearable
            @keyup.enter="handleQuery"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 120px">
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="等级" prop="userLevel">
          <el-select v-model="queryParams.userLevel" placeholder="用户等级" clearable style="width: 120px">
            <el-option label="普通用户" value="1" />
            <el-option label="VIP用户" value="2" />
            <el-option label="SVIP用户" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="注册时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button 
            type="info" 
            icon="Sort" 
            @click="showSearch = !showSearch"
          >
            {{ showSearch ? '隐藏' : '显示' }}搜索
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 表格数据 -->
      <el-table 
        v-loading="loading" 
        :data="userList" 
        @selection-change="handleSelectionChange"
        stripe
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="用户ID" align="center" prop="userId" width="120" />
        <el-table-column label="头像" align="center" width="80">
          <template #default="scope">
            <el-avatar :size="40" :src="scope.row.avatar">
              {{ scope.row.nickname?.charAt(0) }}
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column label="昵称" align="center" prop="nickname" show-overflow-tooltip />
        <el-table-column label="手机号" align="center" prop="phoneNumber" width="130" />
        <el-table-column label="性别" align="center" prop="gender" width="80">
          <template #default="scope">
            <el-tag :type="getGenderType(scope.row.gender)" size="small">
              {{ getGenderText(scope.row.gender) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="城市" align="center" prop="city" width="100" />
        <el-table-column label="等级" align="center" prop="userLevel" width="100">
          <template #default="scope">
            <el-tag :type="getLevelType(scope.row.userLevel)" size="small">
              {{ getLevelText(scope.row.userLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="对话次数" align="center" prop="totalChats" width="100" sortable />
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="0"
              inactive-value="1"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="注册时间" align="center" prop="registerTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.registerTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="最后登录" align="center" prop="loginDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.loginDate) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template #default="scope">
            <el-button
              type="primary"
              text
              icon="View"
              @click="handleDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              type="danger"
              text
              icon="Delete"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="UserList">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { 
  getUserList, 
  updateUserStatus, 
  deleteUsers, 
  exportUserData 
} from '@/api/chatbot/user.js'
import { parseTime, addDateRange } from '@/utils/ruoyi'
import { download } from '@/utils/request'
import Pagination from '@/components/Pagination/index.vue'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const showSearch = ref(true)
const userList = ref([])
const total = ref(0)
const multiple = ref(true)
const ids = ref([])
const dateRange = ref([])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  nickname: '',
  phoneNumber: '',
  status: '',
  userLevel: ''
})

// 初始化
onMounted(() => {
  getList()
})

// 获取用户列表
const getList = async () => {
  try {
    loading.value = true
    let params = { ...queryParams }
    params = addDateRange(params, dateRange.value, 'registerTime')
    
    const response = await getUserList(params)
    userList.value = response.rows || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  dateRange.value = []
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    nickname: '',
    phoneNumber: '',
    status: '',
    userLevel: ''
  })
  getList()
}

// 多选
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.userId)
  multiple.value = !selection.length
}

// 状态切换
const handleStatusChange = async (row) => {
  try {
    const text = row.status === '0' ? '启用' : '停用'
    await ElMessageBox.confirm(`确认要${text}用户"${row.nickname}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await updateUserStatus(row.userId, row.status)
    ElMessage.success(`${text}成功`)
  } catch (error) {
    // 恢复原状态
    row.status = row.status === '0' ? '1' : '0'
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 查看详情
const handleDetail = (row) => {
  router.push(`/chatbot/user/detail/${row.userId}`)
}

// 删除用户
const handleDelete = (row) => {
  const userIds = row ? [row.userId] : ids.value
  const names = row ? row.nickname : '选中的用户'
  
  ElMessageBox.confirm(`确定删除用户"${names}"吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteUsers(userIds)
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

// 导出数据
const handleExport = () => {
  download('/api/user/export', {
    ...queryParams
  }, `user_${new Date().getTime()}.xlsx`)
}

// 获取性别类型
const getGenderType = (gender) => {
  const types = { '0': 'primary', '1': 'danger', '2': 'info' }
  return types[gender] || 'info'
}

// 获取性别文本
const getGenderText = (gender) => {
  const texts = { '0': '男', '1': '女', '2': '未知' }
  return texts[gender] || '未知'
}

// 获取等级类型
const getLevelType = (level) => {
  const types = { 1: '', 2: 'warning', 3: 'danger' }
  return types[level] || ''
}

// 获取等级文本
const getLevelText = (level) => {
  const texts = { 1: '普通', 2: 'VIP', 3: 'SVIP' }
  return texts[level] || '普通'
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
