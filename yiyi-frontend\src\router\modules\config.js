/**
 * 路由模块配置文件
 * 统一管理所有模块的路由配置和元信息
 */

// 导入各模块路由
import userRoutes from './user/index.js'
import behaviorRoutes from './behavior/index.js'

// 模块元信息配置
export const MODULE_META = {
  user: {
    name: 'user',
    title: '用户管理',
    description: '用户信息管理、用户统计分析、用户行为追踪',
    version: '1.0.0',
    author: 'Development Team',
    icon: 'user',
    color: '#409eff',
    routes: userRoutes
  },
  behavior: {
    name: 'behavior',
    title: '行为分析',
    description: '用户行为监控、行为统计分析、行为路径追踪',
    version: '1.0.0',
    author: 'Development Team',
    icon: 'data-analysis',
    color: '#67c23a',
    routes: behaviorRoutes
  }
}

// 模块路由配置
export const MODULE_ROUTES = {
  user: userRoutes,
  behavior: behaviorRoutes
}

// 获取所有模块路由
export function getAllModuleRoutes() {
  return Object.values(MODULE_ROUTES).flat()
}

// 获取指定模块路由
export function getModuleRoutes(moduleName) {
  return MODULE_ROUTES[moduleName] || []
}

// 获取模块元信息
export function getModuleMeta(moduleName) {
  return MODULE_META[moduleName] || null
}

// 获取所有模块元信息
export function getAllModuleMeta() {
  return MODULE_META
}

// 检查模块是否存在
export function hasModule(moduleName) {
  return moduleName in MODULE_META
}

// 获取模块列表
export function getModuleList() {
  return Object.keys(MODULE_META)
}

// 模块路由统计信息
export function getModuleStats() {
  const stats = {}
  
  Object.entries(MODULE_META).forEach(([name, meta]) => {
    stats[name] = {
      title: meta.title,
      routeCount: meta.routes.length,
      version: meta.version,
      author: meta.author
    }
  })
  
  return stats
}

export default {
  MODULE_META,
  MODULE_ROUTES,
  getAllModuleRoutes,
  getModuleRoutes,
  getModuleMeta,
  getAllModuleMeta,
  hasModule,
  getModuleList,
  getModuleStats
}
