@echo off
echo ========================================
echo 智能客服系统启动脚本
echo ========================================

echo.
echo 正在启动后端模拟服务器...
cd mock-server
start "后端服务" cmd /k "npm start"
cd ..

echo.
echo 等待后端服务启动...
timeout /t 3 /nobreak >nul

echo.
echo 正在启动前端开发服务器...
start "前端服务" cmd /k "npm run dev"

echo.
echo 等待前端服务启动...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo 服务启动完成！
echo ========================================
echo.
echo 前端地址: http://localhost:80
echo 后端地址: http://localhost:8090
echo 测试页面: http://localhost:80/test-chatbot.html
echo.
echo 智能客服页面:
echo - 客服对话: http://localhost:80/chatbot/index
echo - 会话管理: http://localhost:80/chatbot/session
echo.
echo 按任意键打开浏览器...
pause >nul

start http://localhost:80
start http://localhost:80/test-chatbot.html

echo.
echo 浏览器已打开，请查看智能客服系统！
echo 按任意键退出...
pause >nul
