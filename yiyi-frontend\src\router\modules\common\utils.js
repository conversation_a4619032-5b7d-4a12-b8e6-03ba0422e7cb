/**
 * 路由模块通用工具函数
 * 提供路由配置、权限检查、菜单生成等通用功能
 */

/**
 * 生成路由配置
 * @param {Object} config 路由配置对象
 * @returns {Object} 标准化的路由配置
 */
export function createRoute(config) {
  const {
    path,
    component,
    name,
    meta = {},
    children = [],
    redirect,
    alias
  } = config

  return {
    path,
    component,
    name,
    meta: {
      title: meta.title || name,
      icon: meta.icon || 'menu',
      hidden: meta.hidden || false,
      keepAlive: meta.keepAlive || false,
      permissions: meta.permissions || [],
      activeMenu: meta.activeMenu,
      description: meta.description || '',
      ...meta
    },
    children,
    redirect,
    alias
  }
}

/**
 * 批量创建路由配置
 * @param {Array} configs 路由配置数组
 * @returns {Array} 标准化的路由配置数组
 */
export function createRoutes(configs) {
  return configs.map(config => createRoute(config))
}

/**
 * 生成面包屑导航数据
 * @param {Object} route 当前路由对象
 * @param {Array} routes 路由配置数组
 * @returns {Array} 面包屑数据
 */
export function generateBreadcrumb(route, routes) {
  const breadcrumb = []
  const pathArray = route.path.split('/').filter(Boolean)
  
  let currentPath = ''
  pathArray.forEach(segment => {
    currentPath += `/${segment}`
    const matchedRoute = findRouteByPath(currentPath, routes)
    if (matchedRoute && !matchedRoute.meta.hidden) {
      breadcrumb.push({
        title: matchedRoute.meta.title,
        path: currentPath,
        icon: matchedRoute.meta.icon
      })
    }
  })
  
  return breadcrumb
}

/**
 * 根据路径查找路由配置
 * @param {string} path 路径
 * @param {Array} routes 路由配置数组
 * @returns {Object|null} 匹配的路由配置
 */
export function findRouteByPath(path, routes) {
  for (const route of routes) {
    if (route.path === path) {
      return route
    }
    if (route.children && route.children.length > 0) {
      const found = findRouteByPath(path, route.children)
      if (found) return found
    }
  }
  return null
}

/**
 * 根据权限过滤路由
 * @param {Array} routes 路由配置数组
 * @param {Array} userPermissions 用户权限列表
 * @returns {Array} 过滤后的路由配置
 */
export function filterRoutesByPermissions(routes, userPermissions = []) {
  return routes.filter(route => {
    // 如果路由没有权限要求，直接显示
    if (!route.meta.permissions || route.meta.permissions.length === 0) {
      return true
    }
    
    // 检查用户是否有任一权限
    const hasPermission = route.meta.permissions.some(permission => 
      userPermissions.includes(permission)
    )
    
    if (hasPermission && route.children) {
      // 递归过滤子路由
      route.children = filterRoutesByPermissions(route.children, userPermissions)
    }
    
    return hasPermission
  })
}

/**
 * 生成侧边栏菜单数据
 * @param {Array} routes 路由配置数组
 * @param {Array} userPermissions 用户权限列表
 * @returns {Array} 菜单数据
 */
export function generateMenus(routes, userPermissions = []) {
  const filteredRoutes = filterRoutesByPermissions(routes, userPermissions)
  
  return filteredRoutes
    .filter(route => !route.meta.hidden)
    .map(route => ({
      id: route.name,
      title: route.meta.title,
      icon: route.meta.icon,
      path: route.path,
      children: route.children ? generateMenus(route.children, userPermissions) : []
    }))
}

/**
 * 验证路由配置
 * @param {Object} route 路由配置
 * @returns {Object} 验证结果
 */
export function validateRoute(route) {
  const errors = []
  const warnings = []
  
  // 必需字段检查
  if (!route.path) {
    errors.push('路由路径(path)不能为空')
  }
  
  if (!route.component && !route.redirect) {
    errors.push('路由必须指定组件(component)或重定向(redirect)')
  }
  
  if (!route.name) {
    warnings.push('建议为路由指定名称(name)')
  }
  
  if (!route.meta || !route.meta.title) {
    warnings.push('建议为路由指定标题(meta.title)')
  }
  
  // 路径格式检查
  if (route.path && !route.path.startsWith('/') && !route.path.includes(':')) {
    warnings.push('路由路径建议以"/"开头或包含参数')
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 批量验证路由配置
 * @param {Array} routes 路由配置数组
 * @returns {Object} 验证结果汇总
 */
export function validateRoutes(routes) {
  const results = routes.map((route, index) => ({
    index,
    route: route.name || route.path,
    ...validateRoute(route)
  }))
  
  const totalErrors = results.reduce((sum, result) => sum + result.errors.length, 0)
  const totalWarnings = results.reduce((sum, result) => sum + result.warnings.length, 0)
  
  return {
    valid: totalErrors === 0,
    totalErrors,
    totalWarnings,
    results
  }
}

/**
 * 路由配置调试信息
 * @param {Array} routes 路由配置数组
 * @returns {Object} 调试信息
 */
export function getRouteDebugInfo(routes) {
  const flatRoutes = flattenRoutes(routes)
  
  return {
    totalRoutes: flatRoutes.length,
    hiddenRoutes: flatRoutes.filter(route => route.meta.hidden).length,
    protectedRoutes: flatRoutes.filter(route => route.meta.permissions?.length > 0).length,
    cachedRoutes: flatRoutes.filter(route => route.meta.keepAlive).length,
    routePaths: flatRoutes.map(route => route.path),
    routeNames: flatRoutes.map(route => route.name).filter(Boolean)
  }
}

/**
 * 扁平化路由配置
 * @param {Array} routes 路由配置数组
 * @returns {Array} 扁平化的路由数组
 */
export function flattenRoutes(routes) {
  const result = []
  
  function traverse(routeList) {
    routeList.forEach(route => {
      result.push(route)
      if (route.children && route.children.length > 0) {
        traverse(route.children)
      }
    })
  }
  
  traverse(routes)
  return result
}

export default {
  createRoute,
  createRoutes,
  generateBreadcrumb,
  findRouteByPath,
  filterRoutesByPermissions,
  generateMenus,
  validateRoute,
  validateRoutes,
  getRouteDebugInfo,
  flattenRoutes
}
