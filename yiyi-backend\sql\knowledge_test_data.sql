-- 知识库管理系统测试数据

-- 清空现有数据
DELETE
FROM knowledge_base
WHERE knowledge_id > 4;
DELETE
FROM knowledge_category
WHERE category_id > 4;

-- 插入更多分类数据
INSERT INTO `knowledge_category`
VALUES (5, 1, '账户问题', 'ACCOUNT', 1, '1', 'admin', NOW(), '', NULL, '账户相关问题'),
       (6, 1, '支付问题', 'PAYMENT', 2, '1', 'admin', NOW(), '', NULL, '支付相关问题'),
       (7, 2, '手机产品', 'MOBILE', 1, '1', 'admin', NOW(), '', NULL, '手机类产品'),
       (8, 2, '电脑产品', 'COMPUTER', 2, '1', 'admin', NOW(), '', NULL, '电脑类产品'),
       (9, 3, '保修服务', 'WARRANTY', 1, '1', 'admin', NOW(), '', NULL, '保修相关服务'),
       (10, 3, '维修服务', 'REPAIR', 2, '1', 'admin', NOW(), '', NULL, '维修相关服务');

-- 插入更多知识库数据
INSERT INTO `knowledge_base`
VALUES (5, 5, '如何注册账户', '怎么注册新账户？',
        '注册账户步骤：\n1. 点击注册按钮\n2. 填写手机号码\n3. 输入验证码\n4. 设置密码\n5. 完成注册',
        '注册,账户,手机,验证码', '注册', 8, '1', 1, 'admin', NOW(), '', NULL, ''),
       (6, 5, '忘记密码怎么办', '密码忘记了如何找回？',
        '找回密码方法：\n1. 点击忘记密码\n2. 输入注册手机号\n3. 获取验证码\n4. 设置新密码\n5. 登录成功',
        '密码,找回,手机号,验证码', '密码', 12, '1', 2, 'admin', NOW(), '', NULL, ''),
       (7, 6, '支付方式有哪些', '支持哪些支付方式？',
        '我们支持以下支付方式：\n1. 微信支付\n2. 支付宝\n3. 银行卡支付\n4. 花呗分期\n5. 信用卡支付',
        '支付,微信,支付宝,银行卡', '支付', 25, '1', 1, 'admin', NOW(), '', NULL, ''),
       (8, 6, '支付失败怎么办', '支付时提示失败怎么处理？',
        '支付失败解决方案：\n1. 检查网络连接\n2. 确认账户余额\n3. 更换支付方式\n4. 联系银行客服\n5. 联系我们客服',
        '支付失败,网络,余额,客服', '支付', 18, '1', 2, 'admin', NOW(), '', NULL, ''),
       (9, 7, '手机如何选择', '如何选择合适的手机？',
        '选择手机建议：\n1. 确定预算范围\n2. 考虑使用需求\n3. 比较性能参数\n4. 查看用户评价\n5. 体验实机操作',
        '手机,选择,预算,性能', '手机', 30, '1', 1, 'admin', NOW(), '', NULL, ''),
       (10, 7, '手机保护套推荐', '什么手机保护套比较好？',
        '手机保护套推荐：\n1. 硅胶套：防摔耐用\n2. 皮革套：美观大方\n3. 透明套：展示原机\n4. 防水套：户外使用\n5. 支架套：观影方便',
        '保护套,硅胶,皮革,防水', '配件', 15, '1', 2, 'admin', NOW(), '', NULL, ''),
       (11, 8, '电脑配置推荐', '什么配置的电脑比较好？',
        '电脑配置建议：\n1. 办公用：i5+8G+256G SSD\n2. 游戏用：i7+16G+512G SSD+独显\n3. 设计用：i7+32G+1T SSD+专业显卡\n4. 学习用：i3+8G+256G SSD\n5. 预算有限：AMD R5+8G+256G',
        '电脑,配置,CPU,内存,硬盘', '电脑', 22, '1', 1, 'admin', NOW(), '', NULL, ''),
       (12, 8, '电脑运行慢怎么办', '电脑越来越慢怎么解决？',
        '电脑加速方法：\n1. 清理垃圾文件\n2. 卸载无用软件\n3. 升级内存条\n4. 更换固态硬盘\n5. 重装操作系统',
        '电脑慢,清理,升级,重装', '维护', 35, '1', 2, 'admin', NOW(), '', NULL, ''),
       (13, 9, '保修期多长时间', '产品保修期是多久？',
        '保修期说明：\n1. 手机：1年质保\n2. 电脑：2年质保\n3. 配件：6个月质保\n4. 人为损坏不在保修范围\n5. 保修期内免费维修',
        '保修期,质保,免费维修', '保修', 28, '1', 1, 'admin', NOW(), '', NULL, ''),
       (14, 9, '如何申请保修', '怎么申请产品保修？',
        '保修申请流程：\n1. 准备购买凭证\n2. 联系客服申请\n3. 描述故障现象\n4. 邮寄或送修产品\n5. 等待维修完成',
        '保修申请,凭证,客服,邮寄', '保修', 20, '1', 2, 'admin', NOW(), '', NULL, ''),
       (15, 10, '维修费用标准', '维修需要多少费用？',
        '维修费用说明：\n1. 保修期内：免费维修\n2. 屏幕更换：200-800元\n3. 电池更换：100-300元\n4. 主板维修：300-1000元\n5. 具体费用需检测后确定',
        '维修费用,屏幕,电池,主板', '维修', 16, '1', 1, 'admin', NOW(), '', NULL, ''),
       (16, 10, '维修需要多长时间', '维修大概需要几天？',
        '维修时间说明：\n1. 简单故障：1-2天\n2. 复杂故障：3-7天\n3. 需要配件：7-15天\n4. 特殊情况：15-30天\n5. 会及时通知进度',
        '维修时间,配件,进度通知', '维修', 24, '1', 2, 'admin', NOW(), '', NULL, ''),
       (17, 1, '客服工作时间', '客服什么时候上班？',
        '客服工作时间：\n周一至周五：9:00-18:00\n周六至周日：10:00-17:00\n节假日：10:00-16:00\n24小时在线客服：智能机器人',
        '客服时间,工作日,周末,节假日', '客服', 40, '1', 3, 'admin', NOW(), '', NULL, ''),
       (18, 1, '如何投诉建议', '有问题如何投诉？',
        '投诉建议渠道：\n1. 客服热线：400-123-4567\n2. 在线客服：网站右下角\n3. 邮箱：<EMAIL>\n4. 微信公众号：留言反馈\n5. 官方QQ群：123456789',
        '投诉,建议,热线,邮箱,微信', '投诉', 12, '1', 4, 'admin', NOW(), '', NULL, ''),
       (19, 4, '订单状态说明', '订单状态都代表什么？',
        '订单状态含义：\n1. 待付款：等待用户付款\n2. 待发货：已付款等待发货\n3. 已发货：商品已发出\n4. 待收货：运输中\n5. 已完成：交易完成',
        '订单状态,付款,发货,收货', '订单', 33, '1', 1, 'admin', NOW(), '', NULL, ''),
       (20, 4, '如何取消订单', '不想要了怎么取消订单？',
        '取消订单方法：\n1. 待付款：直接取消\n2. 已付款未发货：联系客服\n3. 已发货：拒收退款\n4. 已收货：申请退货\n5. 超时自动取消',
        '取消订单,退款,退货,拒收', '订单', 27, '1', 2, 'admin', NOW(), '', NULL, '');

-- 更新一些知识库的命中次数，模拟真实使用情况
UPDATE knowledge_base
SET hit_count = hit_count + FLOOR(RAND() * 50)
WHERE knowledge_id <= 20;

-- 插入一些产品信息测试数据
INSERT INTO `product_info`
VALUES (4, 'P004', '游戏笔记本电脑', 3, '联想', '高性能游戏笔记本，RTX4060显卡，16GB内存', 6999.00, 7999.00, 50, 25,
        '["laptop1.jpg","laptop2.jpg"]', '{"cpu":["Intel i7"],"memory":["16GB"],"storage":["512GB SSD"]}',
        '笔记本,游戏,联想,RTX', '1', 1, 'admin', NOW(), '', NULL, ''),
       (5, 'P005', '无线充电器', 2, '小米', '15W快速无线充电，支持多种设备', 199.00, 299.00, 300, 180,
        '["charger1.jpg","charger2.jpg"]', '{"power":["15W"],"compatibility":["iPhone","Android"]}',
        '充电器,无线充电,快充,小米', '1', 2, 'admin', NOW(), '', NULL, ''),
       (6, 'P006', '机械键盘', 4, '雷蛇', '青轴机械键盘，RGB背光，游戏专用', 899.00, 1199.00, 80, 45,
        '["keyboard1.jpg","keyboard2.jpg"]', '{"switch":["青轴"],"backlight":["RGB"],"connection":["有线"]}',
        '键盘,机械键盘,青轴,RGB,雷蛇', '1', 3, 'admin', NOW(), '', NULL, '');

-- 提交事务
COMMIT;
