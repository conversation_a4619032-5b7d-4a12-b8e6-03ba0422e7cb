<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <!-- 全局参数 -->
    <settings>
        <!-- 使全局的映射器启用或禁用缓存 -->
        <setting name="cacheEnabled" value="true"/>
        <!-- 允许JDBC 支持自动生成主键 -->
        <setting name="useGeneratedKeys" value="true"/>
        <!-- 配置默认的执行器.SIMPLE就是普通执行器;REUSE执行器会重用预处理语句(prepared statements);BATCH执行器将重用语句并执行批量更新 -->
        <setting name="defaultExecutorType" value="SIMPLE"/>
        <!-- 指定 MyBatis 所用日志的具体实现 -->
        <setting name="logImpl" value="SLF4J"/>
        <!-- 使用驼峰命名法转换字段 -->
        <!-- <setting name="mapUnderscoreToCamelCase" value="true"/> -->
    </settings>
    <typeAliases>
        <typeAlias alias="SysDept" type="com.yiyi.common.core.domain.entity.SysDept"/>
        <typeAlias alias="SysConfig" type="com.yiyi.system.domain.SysConfig"/>
        <typeAlias alias="SysDictData" type="com.yiyi.common.core.domain.entity.SysDictData"/>
        <typeAlias alias="SysDictType" type="com.yiyi.common.core.domain.entity.SysDictType"/>
        <typeAlias alias="SysLogininfor" type="com.yiyi.system.domain.SysLogininfor"/>
        <typeAlias alias="SysUser" type="com.yiyi.common.core.domain.entity.SysUser"/>
        <typeAlias alias="SysRole" type="com.yiyi.common.core.domain.entity.SysRole"/>
        <typeAlias alias="SysMenu" type="com.yiyi.common.core.domain.entity.SysMenu"/>
        <typeAlias alias="SysOperLog" type="com.yiyi.system.domain.SysOperLog"/>
        <typeAlias alias="SysNotice" type="com.yiyi.system.domain.SysNotice"/>
        <typeAlias alias="SysPost" type="com.yiyi.system.domain.SysPost"/>
        <typeAlias alias="SysRoleDept" type="com.yiyi.system.domain.SysRoleDept"/>
        <typeAlias alias="SysRoleMenu" type="com.yiyi.system.domain.SysRoleMenu"/>
        <typeAlias alias="SysUserPost" type="com.yiyi.system.domain.SysUserPost"/>
        <typeAlias alias="SysUserRole" type="com.yiyi.system.domain.SysUserRole"/>
        <typeAlias alias="SysJob" type="com.yiyi.quartz.domain.SysJob"/>
        <typeAlias alias="SysJobLog" type="com.yiyi.quartz.domain.SysJobLog"/>
    </typeAliases>

</configuration>
