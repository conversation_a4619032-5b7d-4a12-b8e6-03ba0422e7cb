<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="关系类型" prop="relationType">
        <el-select v-model="queryParams.relationType" placeholder="请选择关系类型" clearable>
          <el-option label="附近" value="NEAR_BY" />
          <el-option label="属于" value="BELONGS_TO" />
          <el-option label="相似" value="SIMILAR_TO" />
          <el-option label="包含" value="CONTAINS" />
        </el-select>
      </el-form-item>
      <el-form-item label="源实体" prop="sourceEntityName">
        <el-input
          v-model="queryParams.sourceEntityName"
          placeholder="请输入源实体名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="目标实体" prop="targetEntityName">
        <el-input
          v-model="queryParams.targetEntityName"
          placeholder="请输入目标实体名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="关系状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['chatbot:knowledge:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['chatbot:knowledge:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['chatbot:knowledge:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-share"
          size="mini"
          @click="handleGraphView"
          v-hasPermi="['chatbot:knowledge:query']"
        >关系图谱</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="relationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="关系ID" align="center" prop="relationId" />
      <el-table-column label="源实体" align="center" prop="sourceEntity.entityName" :show-overflow-tooltip="true" />
      <el-table-column label="关系类型" align="center" prop="relationType">
        <template slot-scope="scope">
          <el-tag :type="getRelationTypeTag(scope.row.relationType)">
            {{ getRelationTypeName(scope.row.relationType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="目标实体" align="center" prop="targetEntity.entityName" :show-overflow-tooltip="true" />
      <el-table-column label="关系权重" align="center" prop="weight">
        <template slot-scope="scope">
          <el-progress :percentage="scope.row.weight * 20" :color="getWeightColor(scope.row.weight)"></el-progress>
        </template>
      </el-table-column>
      <el-table-column label="描述" align="center" prop="description" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['chatbot:knowledge:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['chatbot:knowledge:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['chatbot:knowledge:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改知识关系对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="源实体" prop="sourceEntityId">
          <el-select v-model="form.sourceEntityId" placeholder="请选择源实体" filterable remote :remote-method="searchSourceEntity">
            <el-option
              v-for="entity in sourceEntityOptions"
              :key="entity.entityId"
              :label="entity.entityName"
              :value="entity.entityId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关系类型" prop="relationType">
          <el-select v-model="form.relationType" placeholder="请选择关系类型">
            <el-option label="附近" value="NEAR_BY" />
            <el-option label="属于" value="BELONGS_TO" />
            <el-option label="相似" value="SIMILAR_TO" />
            <el-option label="包含" value="CONTAINS" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标实体" prop="targetEntityId">
          <el-select v-model="form.targetEntityId" placeholder="请选择目标实体" filterable remote :remote-method="searchTargetEntity">
            <el-option
              v-for="entity in targetEntityOptions"
              :key="entity.entityId"
              :label="entity.entityName"
              :value="entity.entityId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关系权重" prop="weight">
          <el-slider v-model="form.weight" :min="0" :max="5" :step="0.1" show-input></el-slider>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入关系描述" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 关系详情对话框 -->
    <el-dialog title="关系详情" :visible.sync="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="源实体">{{ detailForm.sourceEntity?.entityName }}</el-descriptions-item>
        <el-descriptions-item label="目标实体">{{ detailForm.targetEntity?.entityName }}</el-descriptions-item>
        <el-descriptions-item label="关系类型">{{ getRelationTypeName(detailForm.relationType) }}</el-descriptions-item>
        <el-descriptions-item label="关系权重">{{ detailForm.weight }}</el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ detailForm.createTime }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ detailForm.description }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 关系图谱对话框 -->
    <el-dialog title="知识图谱关系图" :visible.sync="graphOpen" width="90%" append-to-body>
      <div id="graph-container" style="height: 600px;"></div>
    </el-dialog>
  </div>
</template>

<script>
import { listRelation, getRelation, delRelation, addRelation, updateRelation } from "@/api/chatbot/knowledge/relation";
import { listEntity } from "@/api/chatbot/knowledge/entity";

export default {
  name: "Relation",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 知识关系表格数据
      relationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 详情弹出层
      detailOpen: false,
      // 图谱弹出层
      graphOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        relationType: null,
        sourceEntityName: null,
        targetEntityName: null,
        status: null
      },
      // 表单参数
      form: {},
      // 详情表单
      detailForm: {},
      // 源实体选项
      sourceEntityOptions: [],
      // 目标实体选项
      targetEntityOptions: [],
      // 表单校验
      rules: {
        sourceEntityId: [
          { required: true, message: "源实体不能为空", trigger: "change" }
        ],
        targetEntityId: [
          { required: true, message: "目标实体不能为空", trigger: "change" }
        ],
        relationType: [
          { required: true, message: "关系类型不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询知识关系列表 */
    getList() {
      this.loading = true;
      listRelation(this.queryParams).then(response => {
        this.relationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        relationId: null,
        sourceEntityId: null,
        targetEntityId: null,
        relationType: null,
        description: null,
        weight: 1.0,
        status: "0"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.relationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加知识关系";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const relationId = row.relationId || this.ids
      getRelation(relationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改知识关系";
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      getRelation(row.relationId).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 关系图谱操作 */
    handleGraphView() {
      this.graphOpen = true;
      this.$nextTick(() => {
        this.initGraph();
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.relationId != null) {
            updateRelation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRelation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const relationIds = row.relationId || this.ids;
      this.$modal.confirm('是否确认删除知识关系编号为"' + relationIds + '"的数据项？').then(function() {
        return delRelation(relationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 搜索源实体 */
    searchSourceEntity(query) {
      if (query !== '') {
        listEntity({ entityName: query, pageSize: 20 }).then(response => {
          this.sourceEntityOptions = response.rows;
        });
      }
    },
    /** 搜索目标实体 */
    searchTargetEntity(query) {
      if (query !== '') {
        listEntity({ entityName: query, pageSize: 20 }).then(response => {
          this.targetEntityOptions = response.rows;
        });
      }
    },
    /** 获取关系类型名称 */
    getRelationTypeName(type) {
      const typeMap = {
        'NEAR_BY': '附近',
        'BELONGS_TO': '属于',
        'SIMILAR_TO': '相似',
        'CONTAINS': '包含'
      };
      return typeMap[type] || type;
    },
    /** 获取关系类型标签 */
    getRelationTypeTag(type) {
      const tagMap = {
        'NEAR_BY': 'success',
        'BELONGS_TO': 'primary',
        'SIMILAR_TO': 'warning',
        'CONTAINS': 'info'
      };
      return tagMap[type] || '';
    },
    /** 获取权重颜色 */
    getWeightColor(weight) {
      if (weight >= 4) return '#67c23a';
      if (weight >= 3) return '#e6a23c';
      if (weight >= 2) return '#f56c6c';
      return '#909399';
    },
    /** 初始化关系图谱 */
    initGraph() {
      // 这里可以集成图谱可视化库，如 ECharts、D3.js 等
      console.log('初始化关系图谱');
    }
  }
};
</script>
