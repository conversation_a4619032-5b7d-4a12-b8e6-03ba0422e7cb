-- ----------------------------
-- 智能客服相关表结构
-- ----------------------------

-- ----------------------------
-- 1、对话会话表
-- ----------------------------
DROP TABLE IF EXISTS `chat_session`;
CREATE TABLE `chat_session`
(
    `session_id`        varchar(64) NOT NULL COMMENT '会话ID',
    `user_id`           bigint(20)   DEFAULT NULL COMMENT '用户ID',
    `user_name`         varchar(30)  DEFAULT '' COMMENT '用户名称',
    `session_title`     varchar(100) DEFAULT '' COMMENT '会话标题',
    `status`            char(1)      DEFAULT '1' COMMENT '会话状态（0结束 1进行中）',
    `last_message_time` datetime     DEFAULT NULL COMMENT '最后消息时间',
    `message_count`     int(11)      DEFAULT 0 COMMENT '消息数量',
    `create_time`       datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`            varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`session_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='对话会话表';

-- ----------------------------
-- 2、对话消息表
-- ----------------------------
DROP TABLE IF EXISTS `chat_message`;
CREATE TABLE `chat_message`
(
    `message_id`   bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '消息ID',
    `session_id`   varchar(64) NOT NULL COMMENT '会话ID',
    `sender_type`  char(1)     NOT NULL COMMENT '发送者类型（0用户 1机器人）',
    `sender_id`    bigint(20)    DEFAULT NULL COMMENT '发送者ID',
    `sender_name`  varchar(30)   DEFAULT '' COMMENT '发送者名称',
    `message_type` char(1)       DEFAULT '1' COMMENT '消息类型（1文本 2图片 3文件 4产品卡片）',
    `content`      text COMMENT '消息内容',
    `extra_data`   json          DEFAULT NULL COMMENT '扩展数据（JSON格式）',
    `intent`       varchar(50)   DEFAULT NULL COMMENT '用户意图',
    `confidence`   decimal(3, 2) DEFAULT NULL COMMENT '置信度',
    `create_time`  datetime      DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`message_id`),
    KEY `idx_session_id` (`session_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='对话消息表';

-- ----------------------------
-- 3、知识库表
-- ----------------------------
DROP TABLE IF EXISTS `knowledge_base`;
CREATE TABLE `knowledge_base`
(
    `knowledge_id` bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '知识ID',
    `category_id`  bigint(20)   DEFAULT NULL COMMENT '分类ID',
    `title`        varchar(200) NOT NULL COMMENT '标题',
    `question`     text         NOT NULL COMMENT '问题',
    `answer`       text         NOT NULL COMMENT '答案',
    `keywords`     varchar(500) DEFAULT '' COMMENT '关键词（逗号分隔）',
    `tags`         varchar(200) DEFAULT '' COMMENT '标签',
    `hit_count`    int(11)      DEFAULT 0 COMMENT '命中次数',
    `status`       char(1)      DEFAULT '1' COMMENT '状态（0停用 1启用）',
    `sort_order`   int(11)      DEFAULT 0 COMMENT '排序',
    `create_by`    varchar(64)  DEFAULT '' COMMENT '创建者',
    `create_time`  datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`    varchar(64)  DEFAULT '' COMMENT '更新者',
    `update_time`  datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`       varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`knowledge_id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_status` (`status`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='知识库表';

-- ----------------------------
-- 4、知识库分类表
-- ----------------------------
DROP TABLE IF EXISTS `knowledge_category`;
CREATE TABLE `knowledge_category`
(
    `category_id`   bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    `parent_id`     bigint(20)   DEFAULT 0 COMMENT '父分类ID',
    `category_name` varchar(50) NOT NULL COMMENT '分类名称',
    `category_code` varchar(50)  DEFAULT '' COMMENT '分类编码',
    `sort_order`    int(11)      DEFAULT 0 COMMENT '排序',
    `status`        char(1)      DEFAULT '1' COMMENT '状态（0停用 1启用）',
    `create_by`     varchar(64)  DEFAULT '' COMMENT '创建者',
    `create_time`   datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`     varchar(64)  DEFAULT '' COMMENT '更新者',
    `update_time`   datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`        varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`category_id`),
    KEY `idx_parent_id` (`parent_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='知识库分类表';

-- ----------------------------
-- 5、产品信息表
-- ----------------------------
DROP TABLE IF EXISTS `product_info`;
CREATE TABLE `product_info`
(
    `product_id`     bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '产品ID',
    `product_code`   varchar(50)  NOT NULL COMMENT '产品编码',
    `product_name`   varchar(200) NOT NULL COMMENT '产品名称',
    `category_id`    bigint(20)     DEFAULT NULL COMMENT '分类ID',
    `brand`          varchar(100)   DEFAULT '' COMMENT '品牌',
    `description`    text COMMENT '产品描述',
    `price`          decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `original_price` decimal(10, 2) DEFAULT NULL COMMENT '原价',
    `stock`          int(11)        DEFAULT 0 COMMENT '库存',
    `sales`          int(11)        DEFAULT 0 COMMENT '销量',
    `images`         text COMMENT '产品图片（JSON数组）',
    `specifications` json           DEFAULT NULL COMMENT '规格参数',
    `keywords`       varchar(500)   DEFAULT '' COMMENT '搜索关键词',
    `status`         char(1)        DEFAULT '1' COMMENT '状态（0下架 1上架）',
    `sort_order`     int(11)        DEFAULT 0 COMMENT '排序',
    `create_by`      varchar(64)    DEFAULT '' COMMENT '创建者',
    `create_time`    datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`      varchar(64)    DEFAULT '' COMMENT '更新者',
    `update_time`    datetime       DEFAULT NULL COMMENT '更新时间',
    `remark`         varchar(500)   DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`product_id`),
    UNIQUE KEY `uk_product_code` (`product_code`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_status` (`status`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='产品信息表';

-- ----------------------------
-- 6、用户意图表
-- ----------------------------
DROP TABLE IF EXISTS `user_intent`;
CREATE TABLE `user_intent`
(
    `intent_id`       bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '意图ID',
    `intent_name`     varchar(50) NOT NULL COMMENT '意图名称',
    `intent_code`     varchar(50) NOT NULL COMMENT '意图编码',
    `description`     varchar(200) DEFAULT '' COMMENT '描述',
    `keywords`        text COMMENT '关键词（每行一个）',
    `patterns`        text COMMENT '匹配模式（正则表达式）',
    `response_type`   char(1)      DEFAULT '1' COMMENT '响应类型（1知识库 2产品推荐 3转人工）',
    `response_config` json         DEFAULT NULL COMMENT '响应配置',
    `priority`        int(11)      DEFAULT 0 COMMENT '优先级',
    `status`          char(1)      DEFAULT '1' COMMENT '状态（0停用 1启用）',
    `create_by`       varchar(64)  DEFAULT '' COMMENT '创建者',
    `create_time`     datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`       varchar(64)  DEFAULT '' COMMENT '更新者',
    `update_time`     datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`intent_id`),
    UNIQUE KEY `uk_intent_code` (`intent_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户意图表';

-- ----------------------------
-- 初始化数据
-- ----------------------------

-- 初始化知识库分类
INSERT INTO `knowledge_category`
VALUES (1, 0, '常见问题', 'FAQ', 1, '1', 'admin', NOW(), '', NULL, '常见问题分类'),
       (2, 0, '产品介绍', 'PRODUCT', 2, '1', 'admin', NOW(), '', NULL, '产品介绍分类'),
       (3, 0, '售后服务', 'SERVICE', 3, '1', 'admin', NOW(), '', NULL, '售后服务分类'),
       (4, 0, '订单相关', 'ORDER', 4, '1', 'admin', NOW(), '', NULL, '订单相关分类');

-- 初始化知识库数据
INSERT INTO `knowledge_base`
VALUES (1, 1, '如何联系客服', '怎么联系客服？',
        '您可以通过以下方式联系我们：\n1. 在线客服：工作时间9:00-18:00\n2. 客服电话：400-123-4567\n3. 客服邮箱：<EMAIL>',
        '客服,联系,电话,邮箱', '客服', 0, '1', 1, 'admin', NOW(), '', NULL, ''),
       (2, 2, '产品保修政策', '产品保修多长时间？',
        '我们的产品提供以下保修服务：\n1. 电子产品：1年质保\n2. 家电产品：3年质保\n3. 配件产品：6个月质保\n保修期内免费维修，超出保修期收取相应费用。',
        '保修,质保,维修', '保修', 0, '1', 2, 'admin', NOW(), '', NULL, ''),
       (3, 3, '退货退款流程', '如何申请退货退款？',
        '退货退款流程如下：\n1. 7天内可无理由退货\n2. 联系客服申请退货\n3. 填写退货单并寄回商品\n4. 收到商品后3-5个工作日退款\n注意：商品需保持原包装完好。',
        '退货,退款,流程', '退货', 0, '1', 3, 'admin', NOW(), '', NULL, ''),
       (4, 4, '订单查询方法', '怎么查询订单状态？',
        '您可以通过以下方式查询订单：\n1. 登录账户查看订单详情\n2. 使用订单号在官网查询\n3. 联系客服提供订单号查询\n4. 关注微信公众号查询',
        '订单,查询,状态', '订单', 0, '1', 4, 'admin', NOW(), '', NULL, '');

-- 初始化用户意图
INSERT INTO `user_intent`
VALUES (1, '问候', 'GREETING', '用户问候', '你好\nhello\n您好\n早上好\n下午好\n晚上好',
        '(你好|hello|您好|早上好|下午好|晚上好)', '1', '{
    "type": "greeting",
    "response": "您好！我是智能客服，很高兴为您服务！"
  }', 10, '1', 'admin', NOW(), '', NULL, ''),
       (2, '产品咨询', 'PRODUCT_INQUIRY', '产品相关咨询', '产品\n价格\n多少钱\n介绍\n功能',
        '(产品|价格|多少钱|介绍|功能)', '2', '{
         "type": "product",
         "response": "请告诉我您想了解哪款产品，我来为您详细介绍。"
       }', 8, '1', 'admin', NOW(), '', NULL, ''),
       (3, '订单查询', 'ORDER_INQUIRY', '订单相关查询', '订单\n查询\n物流\n快递\n发货', '(订单|查询|物流|快递|发货)',
        '1', '{
         "type": "order",
         "response": "请提供您的订单号，我来帮您查询订单状态。"
       }', 7, '1', 'admin', NOW(), '', NULL, ''),
       (4, '售后服务', 'AFTER_SALES', '售后服务相关', '退货\n退款\n保修\n维修\n换货', '(退货|退款|保修|维修|换货)', '1',
        '{
          "type": "service",
          "response": "关于售后服务，我来为您详细说明相关政策。"
        }', 6, '1', 'admin', NOW(), '', NULL, '');

-- 初始化产品信息示例
INSERT INTO `product_info`
VALUES (1, 'P001', '智能手机 Pro Max', 1, '苹果', '最新款智能手机，配备A17芯片，6.7英寸屏幕', 8999.00, 9999.00, 100, 50,
        '["image1.jpg","image2.jpg"]', '{
    "color": [
      "黑色",
      "白色",
      "蓝色"
    ],
    "storage": [
      "128GB",
      "256GB",
      "512GB"
    ]
  }', '手机,智能手机,苹果,iPhone', '1', 1, 'admin', NOW(), '', NULL, ''),
       (2, 'P002', '无线蓝牙耳机', 2, '索尼', '降噪无线蓝牙耳机，30小时续航', 1299.00, 1599.00, 200, 120,
        '["earphone1.jpg","earphone2.jpg"]', '{
         "color": [
           "黑色",
           "白色"
         ],
         "type": [
           "入耳式"
         ]
       }', '耳机,蓝牙耳机,无线耳机,降噪', '1', 2, 'admin', NOW(), '', NULL, ''),
       (3, 'P003', '智能手表', 3, '华为', '健康监测智能手表，支持运动追踪', 2199.00, 2499.00, 80, 30,
        '["watch1.jpg","watch2.jpg"]', '{
         "color": [
           "黑色",
           "银色",
           "金色"
         ],
         "size": [
           "42mm",
           "46mm"
         ]
       }', '手表,智能手表,运动手表,健康', '1', 3, 'admin', NOW(), '', NULL, '');
