<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务类型" prop="taskType">
        <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable>
          <el-option label="定时任务" value="SCHEDULED" />
          <el-option label="手动任务" value="MANUAL" />
          <el-option label="触发任务" value="TRIGGER" />
        </el-select>
      </el-form-item>
      <el-form-item label="数据源类型" prop="sourceType">
        <el-select v-model="queryParams.sourceType" placeholder="请选择数据源类型" clearable>
          <el-option label="Web爬虫" value="WEB_CRAWLER" />
          <el-option label="API接口" value="API" />
          <el-option label="数据库" value="DATABASE" />
          <el-option label="文件" value="FILE" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="任务状态" clearable>
          <el-option label="待执行" value="0" />
          <el-option label="执行中" value="1" />
          <el-option label="已完成" value="2" />
          <el-option label="失败" value="3" />
          <el-option label="暂停" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['chatbot:collection:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['chatbot:collection:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['chatbot:collection:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-video-play"
          size="mini"
          :disabled="multiple"
          @click="handleExecute"
          v-hasPermi="['chatbot:collection:execute']"
        >执行</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-data-analysis"
          size="mini"
          @click="handleStats"
          v-hasPermi="['chatbot:collection:query']"
        >统计</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务ID" align="center" prop="taskId" width="80" />
      <el-table-column label="任务名称" align="center" prop="taskName" :show-overflow-tooltip="true" />
      <el-table-column label="任务类型" align="center" prop="taskType">
        <template slot-scope="scope">
          <el-tag :type="getTaskTypeTag(scope.row.taskType)">
            {{ getTaskTypeName(scope.row.taskType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="数据源类型" align="center" prop="sourceType">
        <template slot-scope="scope">
          <el-tag :type="getSourceTypeTag(scope.row.sourceType)">
            {{ getSourceTypeName(scope.row.sourceType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="目标实体" align="center" prop="targetEntityType" />
      <el-table-column label="执行统计" align="center" width="120">
        <template slot-scope="scope">
          <div>总计: {{ scope.row.executeCount }}</div>
          <div style="color: #67c23a;">成功: {{ scope.row.successCount }}</div>
          <div style="color: #f56c6c;">失败: {{ scope.row.failureCount }}</div>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="getStatusTag(scope.row.status)">
            {{ getStatusName(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="最后执行" align="center" prop="lastExecuteTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastExecuteTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-video-play"
            @click="handleExecute(scope.row)"
            v-hasPermi="['chatbot:collection:execute']"
            :disabled="scope.row.status === '1'"
          >执行</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['chatbot:collection:edit']"
          >修改</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['chatbot:collection:edit']">
            <span class="el-dropdown-link">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="detail" icon="el-icon-view">详情</el-dropdown-item>
              <el-dropdown-item command="test" icon="el-icon-connection">测试连接</el-dropdown-item>
              <el-dropdown-item command="preview" icon="el-icon-view">预览数据</el-dropdown-item>
              <el-dropdown-item command="pause" icon="el-icon-video-pause" v-if="scope.row.status === '0'">暂停</el-dropdown-item>
              <el-dropdown-item command="resume" icon="el-icon-video-play" v-if="scope.row.status === '4'">恢复</el-dropdown-item>
              <el-dropdown-item command="delete" icon="el-icon-delete">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数据采集任务对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-row>
              <el-col :span="12">
                <el-form-item label="任务名称" prop="taskName">
                  <el-input v-model="form.taskName" placeholder="请输入任务名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="任务类型" prop="taskType">
                  <el-select v-model="form.taskType" placeholder="请选择任务类型">
                    <el-option label="定时任务" value="SCHEDULED" />
                    <el-option label="手动任务" value="MANUAL" />
                    <el-option label="触发任务" value="TRIGGER" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="数据源类型" prop="sourceType">
                  <el-select v-model="form.sourceType" placeholder="请选择数据源类型" @change="handleSourceTypeChange">
                    <el-option label="Web爬虫" value="WEB_CRAWLER" />
                    <el-option label="API接口" value="API" />
                    <el-option label="数据库" value="DATABASE" />
                    <el-option label="文件" value="FILE" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="目标实体类型" prop="targetEntityType">
                  <el-select v-model="form.targetEntityType" placeholder="请选择目标实体类型">
                    <el-option label="景点" value="ATTRACTION" />
                    <el-option label="美食" value="FOOD" />
                    <el-option label="酒店" value="HOTEL" />
                    <el-option label="交通" value="TRANSPORT" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="Cron表达式" prop="cronExpression" v-if="form.taskType === 'SCHEDULED'">
              <el-input v-model="form.cronExpression" placeholder="请输入Cron表达式，如：0 0 2 * * ?">
                <template slot="append">
                  <el-button @click="showCronHelper">帮助</el-button>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="数据源配置" name="source">
            <el-form-item label="配置信息">
              <el-input
                v-model="sourceConfigText"
                type="textarea"
                :rows="10"
                placeholder="请输入JSON格式的数据源配置"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="testConnection">测试连接</el-button>
              <el-button type="success" @click="loadTemplate">加载模板</el-button>
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="采集规则" name="rules">
            <el-form-item label="采集规则">
              <el-input
                v-model="collectionRulesText"
                type="textarea"
                :rows="8"
                placeholder="请输入JSON格式的采集规则"
              />
            </el-form-item>
            <el-form-item label="处理规则">
              <el-input
                v-model="processingRulesText"
                type="textarea"
                :rows="6"
                placeholder="请输入JSON格式的数据处理规则"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="warning" @click="previewData">预览数据</el-button>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog title="任务详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="任务名称">{{ detailForm.taskName }}</el-descriptions-item>
        <el-descriptions-item label="任务类型">{{ getTaskTypeName(detailForm.taskType) }}</el-descriptions-item>
        <el-descriptions-item label="数据源类型">{{ getSourceTypeName(detailForm.sourceType) }}</el-descriptions-item>
        <el-descriptions-item label="目标实体类型">{{ detailForm.targetEntityType }}</el-descriptions-item>
        <el-descriptions-item label="执行次数">{{ detailForm.executeCount }}</el-descriptions-item>
        <el-descriptions-item label="成功次数">{{ detailForm.successCount }}</el-descriptions-item>
        <el-descriptions-item label="失败次数">{{ detailForm.failureCount }}</el-descriptions-item>
        <el-descriptions-item label="成功率">{{ getSuccessRate(detailForm) }}%</el-descriptions-item>
        <el-descriptions-item label="最后执行时间" :span="2">{{ detailForm.lastExecuteTime }}</el-descriptions-item>
        <el-descriptions-item label="下次执行时间" :span="2">{{ detailForm.nextExecuteTime }}</el-descriptions-item>
        <el-descriptions-item label="最后执行结果" :span="2">{{ detailForm.lastResult }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 数据预览对话框 -->
    <el-dialog title="数据预览" :visible.sync="previewOpen" width="90%" append-to-body>
      <el-table :data="previewData" max-height="400">
        <el-table-column
          v-for="(value, key) in previewColumns"
          :key="key"
          :prop="key"
          :label="key"
          :show-overflow-tooltip="true"
        />
      </el-table>
    </el-dialog>

    <!-- 统计信息对话框 -->
    <el-dialog title="采集统计" :visible.sync="statsOpen" width="800px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ stats.totalTasks }}</div>
            <div class="stat-label">总任务数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ stats.runningTasks }}</div>
            <div class="stat-label">执行中</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ stats.totalSuccesses }}</div>
            <div class="stat-label">总成功数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ stats.successRate.toFixed(1) }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { listTask, getTask, delTask, addTask, updateTask, executeTask, testConnection, previewData, getStats } from "@/api/chatbot/collection/task";

export default {
  name: "CollectionTask",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据采集任务表格数据
      taskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 详情弹出层
      detailOpen: false,
      // 预览弹出层
      previewOpen: false,
      // 统计弹出层
      statsOpen: false,
      // 活动标签页
      activeTab: 'basic',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        taskType: null,
        sourceType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 详情表单
      detailForm: {},
      // 数据源配置文本
      sourceConfigText: '',
      // 采集规则文本
      collectionRulesText: '',
      // 处理规则文本
      processingRulesText: '',
      // 预览数据
      previewData: [],
      // 预览列
      previewColumns: {},
      // 统计数据
      stats: {},
      // 表单校验
      rules: {
        taskName: [
          { required: true, message: "任务名称不能为空", trigger: "blur" }
        ],
        taskType: [
          { required: true, message: "任务类型不能为空", trigger: "change" }
        ],
        sourceType: [
          { required: true, message: "数据源类型不能为空", trigger: "change" }
        ],
        targetEntityType: [
          { required: true, message: "目标实体类型不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询数据采集任务列表 */
    getList() {
      this.loading = true;
      listTask(this.queryParams).then(response => {
        this.taskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        taskId: null,
        taskName: null,
        taskType: null,
        sourceType: null,
        sourceConfig: null,
        collectionRules: null,
        processingRules: null,
        targetEntityType: null,
        cronExpression: null,
        remark: null
      };
      this.sourceConfigText = '';
      this.collectionRulesText = '';
      this.processingRulesText = '';
      this.activeTab = 'basic';
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.taskId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数据采集任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const taskId = row.taskId || this.ids
      getTask(taskId).then(response => {
        this.form = response.data;
        this.sourceConfigText = this.form.sourceConfig || '';
        this.collectionRulesText = this.form.collectionRules || '';
        this.processingRulesText = this.form.processingRules || '';
        this.open = true;
        this.title = "修改数据采集任务";
      });
    },
    /** 执行任务 */
    handleExecute(row) {
      const taskIds = row ? [row.taskId] : this.ids;
      this.$modal.confirm('是否确认执行选中的数据采集任务？').then(function() {
        return executeTask(taskIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("任务执行成功");
      }).catch(() => {});
    },
    /** 统计信息 */
    handleStats() {
      getStats().then(response => {
        this.stats = response.data;
        this.statsOpen = true;
      });
    },
    /** 下拉菜单命令处理 */
    handleCommand(command, row) {
      switch (command) {
        case 'detail':
          this.handleDetail(row);
          break;
        case 'test':
          this.handleTestConnection(row);
          break;
        case 'preview':
          this.handlePreviewData(row);
          break;
        case 'pause':
          this.handlePause(row);
          break;
        case 'resume':
          this.handleResume(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
      }
    },
    /** 详情操作 */
    handleDetail(row) {
      getTask(row.taskId).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 测试连接 */
    handleTestConnection(row) {
      const config = JSON.parse(row.sourceConfig);
      testConnection({
        sourceType: row.sourceType,
        sourceConfig: config
      }).then(response => {
        if (response.data.success) {
          this.$modal.msgSuccess("连接测试成功");
        } else {
          this.$modal.msgError("连接测试失败: " + response.data.message);
        }
      });
    },
    /** 预览数据 */
    handlePreviewData(row) {
      const config = JSON.parse(row.sourceConfig);
      const rules = JSON.parse(row.collectionRules);
      previewData({
        sourceType: row.sourceType,
        sourceConfig: config,
        rules: rules,
        limit: 10
      }).then(response => {
        this.previewData = response.data;
        if (this.previewData.length > 0) {
          this.previewColumns = this.previewData[0];
        }
        this.previewOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 验证JSON格式
          try {
            this.form.sourceConfig = this.sourceConfigText;
            this.form.collectionRules = this.collectionRulesText;
            this.form.processingRules = this.processingRulesText;
            
            if (this.sourceConfigText) JSON.parse(this.sourceConfigText);
            if (this.collectionRulesText) JSON.parse(this.collectionRulesText);
            if (this.processingRulesText) JSON.parse(this.processingRulesText);
          } catch (e) {
            this.$modal.msgError("JSON格式错误: " + e.message);
            return;
          }
          
          if (this.form.taskId != null) {
            updateTask(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTask(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const taskIds = row.taskId || this.ids;
      this.$modal.confirm('是否确认删除数据采集任务编号为"' + taskIds + '"的数据项？').then(function() {
        return delTask(taskIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 数据源类型改变 */
    handleSourceTypeChange(value) {
      // 可以根据数据源类型加载不同的配置模板
    },
    /** 获取任务类型名称 */
    getTaskTypeName(type) {
      const typeMap = {
        'SCHEDULED': '定时任务',
        'MANUAL': '手动任务',
        'TRIGGER': '触发任务'
      };
      return typeMap[type] || type;
    },
    /** 获取数据源类型名称 */
    getSourceTypeName(type) {
      const typeMap = {
        'WEB_CRAWLER': 'Web爬虫',
        'API': 'API接口',
        'DATABASE': '数据库',
        'FILE': '文件'
      };
      return typeMap[type] || type;
    },
    /** 获取状态名称 */
    getStatusName(status) {
      const statusMap = {
        '0': '待执行',
        '1': '执行中',
        '2': '已完成',
        '3': '失败',
        '4': '暂停'
      };
      return statusMap[status] || status;
    },
    /** 获取任务类型标签 */
    getTaskTypeTag(type) {
      const tagMap = {
        'SCHEDULED': 'primary',
        'MANUAL': 'success',
        'TRIGGER': 'warning'
      };
      return tagMap[type] || '';
    },
    /** 获取数据源类型标签 */
    getSourceTypeTag(type) {
      const tagMap = {
        'WEB_CRAWLER': 'primary',
        'API': 'success',
        'DATABASE': 'info',
        'FILE': 'warning'
      };
      return tagMap[type] || '';
    },
    /** 获取状态标签 */
    getStatusTag(status) {
      const tagMap = {
        '0': 'info',
        '1': 'warning',
        '2': 'success',
        '3': 'danger',
        '4': 'info'
      };
      return tagMap[status] || '';
    },
    /** 获取成功率 */
    getSuccessRate(task) {
      if (task.executeCount === 0) return 0;
      return ((task.successCount / task.executeCount) * 100).toFixed(1);
    }
  }
};
</script>

<style scoped>
.stat-card {
  text-align: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #fff;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}
</style>
