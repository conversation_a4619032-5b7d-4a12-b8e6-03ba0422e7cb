<template>
  <div class="user-activity">
    <!-- 活跃度折线图 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户活跃度分析</span>
          <div>
            <el-radio-group v-model="activityPeriod" @change="loadActivityData" size="small">
              <el-radio-button value="today">今日</el-radio-button>
              <el-radio-button value="week">本周</el-radio-button>
              <el-radio-button value="month">本月</el-radio-button>
            </el-radio-group>
            <el-button type="primary" size="small" @click="refresh" style="margin-left: 10px;">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      <div ref="activityChartRef" style="height: 300px;" v-loading="activityLoading"></div>
    </el-card>

    <!-- 行为热力图 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>用户行为热力图</span>
          <el-radio-group v-model="heatmapType" @change="loadHeatmapData" size="small">
            <el-radio-button value="hourly">按小时</el-radio-button>
            <el-radio-button value="daily">按天</el-radio-button>
            <el-radio-button value="weekly">按周</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <div ref="heatmapChartRef" style="height: 350px;" v-loading="heatmapLoading"></div>
    </el-card>

    <!-- 时段活跃分析 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 活跃时段统计 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>活跃时段统计</span>
          </template>
          <div class="time-stats">
            <div 
              v-for="period in timeStats" 
              :key="period.name"
              class="time-item"
            >
              <div class="time-info">
                <div class="time-name">{{ period.name }}</div>
                <div class="time-range">{{ period.range }}</div>
              </div>
              <div class="time-progress">
                <el-progress 
                  :percentage="period.percentage" 
                  :color="period.color"
                  :show-text="false"
                />
              </div>
              <div class="time-count">{{ period.count }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 活跃用户排行 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>活跃用户排行</span>
              <el-button text @click="loadActiveUsers">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="active-users" v-loading="usersLoading">
            <div 
              v-for="(user, index) in activeUsers" 
              :key="user.userId"
              class="active-user-item"
            >
              <div class="user-rank">
                <span class="rank-number" :class="getRankClass(index)">{{ index + 1 }}</span>
              </div>
              <el-avatar :size="32" :src="user.avatar">
                {{ user.nickname?.charAt(0) }}
              </el-avatar>
              <div class="user-info">
                <div class="user-name">{{ user.nickname }}</div>
                <div class="user-activity">{{ user.activityScore }}分</div>
              </div>
              <div class="user-stats">
                <div class="stat-item">
                  <span class="stat-label">对话</span>
                  <span class="stat-value">{{ user.chatCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">搜索</span>
                  <span class="stat-value">{{ user.searchCount }}</span>
                </div>
              </div>
            </div>
            <el-empty v-if="activeUsers.length === 0" description="暂无数据" :image-size="60" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 活跃度指标 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <span>活跃度指标</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="metric-item">
            <div class="metric-icon dau">
              <el-icon><User /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ metrics.dau || 0 }}</div>
              <div class="metric-label">日活用户(DAU)</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="metric-item">
            <div class="metric-icon wau">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ metrics.wau || 0 }}</div>
              <div class="metric-label">周活用户(WAU)</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="metric-item">
            <div class="metric-icon mau">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ metrics.mau || 0 }}</div>
              <div class="metric-label">月活用户(MAU)</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="metric-item">
            <div class="metric-icon retention">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ metrics.retention || 0 }}%</div>
              <div class="metric-label">留存率</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { User, UserFilled, Connection, TrendCharts, Refresh } from '@element-plus/icons-vue'
import { getUserActivityData } from '@/api/chatbot/dashboard.js'
import { getUserActivity } from '@/api/chatbot/behavior.js'
import * as echarts from 'echarts'

// Props
const props = defineProps({
  refreshInterval: {
    type: Number,
    default: 60000 // 60秒
  }
})

// 响应式数据
const activityLoading = ref(false)
const heatmapLoading = ref(false)
const usersLoading = ref(false)
const activityPeriod = ref('today')
const heatmapType = ref('hourly')

const metrics = reactive({
  dau: 0,
  wau: 0,
  mau: 0,
  retention: 0
})

const timeStats = ref([
  { name: '早高峰', range: '07:00-09:00', count: 0, percentage: 0, color: '#409eff' },
  { name: '上午', range: '09:00-12:00', count: 0, percentage: 0, color: '#67c23a' },
  { name: '下午', range: '12:00-18:00', count: 0, percentage: 0, color: '#e6a23c' },
  { name: '晚高峰', range: '18:00-21:00', count: 0, percentage: 0, color: '#f56c6c' },
  { name: '夜间', range: '21:00-07:00', count: 0, percentage: 0, color: '#909399' }
])

const activeUsers = ref([])

// 图表引用
const activityChartRef = ref()
const heatmapChartRef = ref()

// 图表实例
let activityChart = null
let heatmapChart = null
let refreshTimer = null

// 初始化
onMounted(() => {
  loadMetrics()
  loadActiveUsers()
  nextTick(() => {
    initCharts()
    startAutoRefresh()
  })
})

// 清理
onUnmounted(() => {
  if (activityChart) activityChart.dispose()
  if (heatmapChart) heatmapChart.dispose()
  if (refreshTimer) clearInterval(refreshTimer)
})

// 加载指标数据
const loadMetrics = async () => {
  try {
    const response = await getUserActivityData({ type: 'metrics' })
    Object.assign(metrics, response.data)
    
    // 更新时段统计
    if (response.data.timeStats) {
      timeStats.value = response.data.timeStats
    }
  } catch (error) {
    console.error('获取活跃度指标失败:', error)
  }
}

// 加载活跃用户
const loadActiveUsers = async () => {
  try {
    usersLoading.value = true
    const response = await getUserActivityData({ type: 'ranking', limit: 10 })
    activeUsers.value = response.data || []
  } catch (error) {
    console.error('获取活跃用户失败:', error)
  } finally {
    usersLoading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  initActivityChart()
  initHeatmapChart()
  
  window.addEventListener('resize', handleResize)
}

// 初始化活跃度图表
const initActivityChart = async () => {
  if (!activityChartRef.value) return
  
  activityChart = echarts.init(activityChartRef.value)
  await loadActivityData()
}

// 加载活跃度数据
const loadActivityData = async () => {
  try {
    activityLoading.value = true
    const response = await getUserActivityData({ 
      type: 'trend', 
      period: activityPeriod.value 
    })
    const data = response.data || []
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['活跃用户', '新增用户', '留存用户'],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.time)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '活跃用户',
          type: 'line',
          data: data.map(item => item.activeUsers),
          smooth: true,
          itemStyle: { color: '#409eff' },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ])
          }
        },
        {
          name: '新增用户',
          type: 'line',
          data: data.map(item => item.newUsers),
          smooth: true,
          itemStyle: { color: '#67c23a' }
        },
        {
          name: '留存用户',
          type: 'line',
          data: data.map(item => item.retentionUsers),
          smooth: true,
          itemStyle: { color: '#e6a23c' }
        }
      ]
    }
    
    activityChart.setOption(option)
  } catch (error) {
    console.error('加载活跃度数据失败:', error)
  } finally {
    activityLoading.value = false
  }
}

// 初始化热力图
const initHeatmapChart = async () => {
  if (!heatmapChartRef.value) return
  
  heatmapChart = echarts.init(heatmapChartRef.value)
  await loadHeatmapData()
}

// 加载热力图数据
const loadHeatmapData = async () => {
  try {
    heatmapLoading.value = true
    const response = await getUserActivity({ type: 'heatmap', period: heatmapType.value })
    const data = response.data || []
    
    let xAxisData, yAxisData
    
    if (heatmapType.value === 'hourly') {
      xAxisData = Array.from({length: 24}, (_, i) => i + ':00')
      yAxisData = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    } else if (heatmapType.value === 'daily') {
      xAxisData = Array.from({length: 31}, (_, i) => (i + 1) + '日')
      yAxisData = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    } else {
      xAxisData = Array.from({length: 52}, (_, i) => '第' + (i + 1) + '周')
      yAxisData = ['2024年']
    }
    
    const option = {
      tooltip: {
        position: 'top'
      },
      grid: {
        height: '60%',
        top: '10%'
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        splitArea: {
          show: true
        }
      },
      yAxis: {
        type: 'category',
        data: yAxisData,
        splitArea: {
          show: true
        }
      },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '15%',
        inRange: {
          color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
        }
      },
      series: [{
        name: '活跃度',
        type: 'heatmap',
        data: data,
        label: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    
    heatmapChart.setOption(option)
  } catch (error) {
    console.error('加载热力图数据失败:', error)
  } finally {
    heatmapLoading.value = false
  }
}

// 开始自动刷新
const startAutoRefresh = () => {
  if (props.refreshInterval > 0) {
    refreshTimer = setInterval(() => {
      loadMetrics()
      loadActivityData()
    }, props.refreshInterval)
  }
}

// 手动刷新
const refresh = () => {
  loadMetrics()
  loadActivityData()
  loadHeatmapData()
  loadActiveUsers()
  ElMessage.success('数据已刷新')
}

// 处理窗口大小变化
const handleResize = () => {
  if (activityChart) activityChart.resize()
  if (heatmapChart) heatmapChart.resize()
}

// 工具函数
const getRankClass = (index) => {
  if (index === 0) return 'rank-first'
  if (index === 1) return 'rank-second'
  if (index === 2) return 'rank-third'
  return 'rank-normal'
}

// 暴露刷新方法
defineExpose({
  refresh
})
</script>

<style scoped>
.user-activity {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 时段统计样式 */
.time-stats {
  padding: 10px 0;
}

.time-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.time-item:last-child {
  margin-bottom: 0;
}

.time-info {
  width: 100px;
  margin-right: 12px;
}

.time-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
}

.time-range {
  font-size: 12px;
  color: #666;
}

.time-progress {
  flex: 1;
  margin-right: 12px;
}

.time-count {
  width: 50px;
  text-align: right;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 活跃用户样式 */
.active-users {
  max-height: 300px;
  overflow-y: auto;
}

.active-user-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.active-user-item:last-child {
  border-bottom: none;
}

.user-rank {
  width: 40px;
  margin-right: 12px;
}

.rank-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.rank-first {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
}

.rank-second {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #333;
}

.rank-third {
  background: linear-gradient(135deg, #cd7f32, #daa520);
}

.rank-normal {
  background-color: #909399;
}

.user-info {
  flex: 1;
  margin-left: 12px;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-activity {
  font-size: 12px;
  color: #409eff;
  font-weight: 500;
}

.user-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 11px;
  color: #999;
  margin-bottom: 2px;
}

.stat-value {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 指标样式 */
.metric-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 100px;
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.metric-icon.dau {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-icon.wau {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.metric-icon.mau {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-icon.retention {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.metric-content {
  flex: 1;
}

.metric-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: #666;
}
</style>
