<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>知识库搜索</span>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-container">
        <div class="search-main">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入搜索关键词..."
            size="large"
            clearable
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #prepend>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button type="primary" @click="handleSearch" :loading="searching">
                搜索
              </el-button>
            </template>
          </el-input>
        </div>

        <!-- 搜索建议 -->
        <div class="search-suggestions" v-if="searchSuggestions.length > 0 && !hasSearched">
          <div class="suggestions-title">热门搜索：</div>
          <el-tag
            v-for="suggestion in searchSuggestions"
            :key="suggestion"
            class="suggestion-tag"
            @click="searchKeyword = suggestion; handleSearch()"
          >
            {{ suggestion }}
          </el-tag>
        </div>
      </div>

      <!-- 高级搜索 -->
      <el-collapse v-model="activeCollapse" class="advanced-search">
        <el-collapse-item title="高级搜索" name="advanced">
          <el-form :model="advancedParams" :inline="true">
            <el-form-item label="分类">
              <el-tree-select
                v-model="advancedParams.categoryId"
                :data="categoryOptions"
                :props="{ value: 'categoryId', label: 'categoryName', children: 'children' }"
                value-key="categoryId"
                placeholder="请选择分类"
                check-strictly
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="问题">
              <el-input
                v-model="advancedParams.question"
                placeholder="请输入问题关键词"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="答案">
              <el-input
                v-model="advancedParams.answer"
                placeholder="请输入答案关键词"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="关键词">
              <el-input
                v-model="advancedParams.keywords"
                placeholder="请输入关键词"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleAdvancedSearch" :loading="searching">
                高级搜索
              </el-button>
              <el-button @click="resetAdvancedSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>

      <!-- 搜索结果统计 -->
      <div class="search-stats" v-if="searchResults.length > 0 || hasSearched">
        <span>找到 <strong>{{ searchResults.length }}</strong> 条相关结果</span>
        <span v-if="searchTime">（用时 {{ searchTime }}ms）</span>
      </div>

      <!-- 搜索结果 -->
      <div class="search-results" v-loading="searching">
        <div v-if="searchResults.length === 0 && hasSearched" class="no-results">
          <el-empty description="未找到相关结果">
            <el-button type="primary" @click="showHotKnowledge">查看热门知识库</el-button>
          </el-empty>
        </div>

        <div v-else>
          <div
            v-for="item in searchResults"
            :key="item.knowledgeId"
            class="result-item"
            @click="handleItemClick(item)"
          >
            <div class="result-header">
              <h3 class="result-title" v-html="highlightKeyword(item.title)"></h3>
              <div class="result-meta">
                <el-tag size="small" type="info">{{ item.categoryName }}</el-tag>
                <span class="hit-count">
                  <el-icon><View /></el-icon>
                  {{ item.hitCount || 0 }}
                </span>
                <span class="result-score" v-if="item.score">
                  匹配度: {{ Math.round(item.score * 100) }}%
                </span>
              </div>
            </div>
            <div class="result-question">
              <strong>问题：</strong><span v-html="highlightKeyword(item.question)"></span>
            </div>
            <div class="result-answer">
              <strong>答案：</strong><span v-html="highlightKeyword(truncateText(item.answer, 200))"></span>
            </div>
            <div class="result-footer">
              <div class="result-tags">
                <el-tag
                  v-for="tag in item.tags?.split(',')"
                  :key="tag"
                  size="small"
                  type="success"
                  style="margin-right: 5px"
                >
                  {{ tag }}
                </el-tag>
              </div>
              <div class="result-keywords">
                <span class="keywords-label">关键词：</span>
                <span class="keywords-text">{{ item.keywords }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 热门知识库 -->
      <div class="hot-knowledge" v-if="showHot">
        <h3>热门知识库</h3>
        <div class="hot-list">
          <div
            v-for="item in hotKnowledge"
            :key="item.knowledgeId"
            class="hot-item"
            @click="handleItemClick(item)"
          >
            <div class="hot-title">{{ item.title }}</div>
            <div class="hot-meta">
              <span class="hot-category">{{ item.categoryName }}</span>
              <span class="hot-count">{{ item.hitCount }} 次</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 知识库详情对话框 -->
    <el-dialog title="知识库详情" v-model="detailVisible" width="800px" append-to-body>
      <el-descriptions :column="1" border v-if="currentItem">
        <el-descriptions-item label="标题">{{ currentItem.title }}</el-descriptions-item>
        <el-descriptions-item label="分类">{{ currentItem.categoryName }}</el-descriptions-item>
        <el-descriptions-item label="问题">{{ currentItem.question }}</el-descriptions-item>
        <el-descriptions-item label="答案">
          <div style="white-space: pre-wrap;">{{ currentItem.answer }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="关键词">{{ currentItem.keywords }}</el-descriptions-item>
        <el-descriptions-item label="标签">{{ currentItem.tags }}</el-descriptions-item>
        <el-descriptions-item label="命中次数">{{ currentItem.hitCount }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="KnowledgeSearch">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { 
  searchKnowledge, 
  fuzzySearchKnowledge, 
  getHotKnowledge,
  getCategoryTree,
  hitKnowledge
} from '@/api/chatbot/knowledge.js'

// 响应式数据
const searching = ref(false)
const searchKeyword = ref('')
const searchResults = ref([])
const hotKnowledge = ref([])
const categoryOptions = ref([])
const hasSearched = ref(false)
const showHot = ref(true)
const searchTime = ref(0)
const detailVisible = ref(false)
const currentItem = ref(null)
const activeCollapse = ref([])
const searchSuggestions = ref(['客服联系方式', '密码修改', '产品功能', '账户问题', '使用教程'])

// 高级搜索参数
const advancedParams = reactive({
  categoryId: null,
  question: '',
  answer: '',
  keywords: ''
})

// 初始化
onMounted(() => {
  loadHotKnowledge()
  loadCategoryOptions()
})

// 加载热门知识库
const loadHotKnowledge = async () => {
  try {
    const response = await getHotKnowledge(10)
    hotKnowledge.value = response.data || []
  } catch (error) {
    console.error('获取热门知识库失败:', error)
  }
}

// 加载分类选项
const loadCategoryOptions = async () => {
  try {
    const response = await getCategoryTree()
    categoryOptions.value = response.data || []
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 基础搜索
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }

  try {
    searching.value = true
    hasSearched.value = true
    showHot.value = false
    
    const startTime = Date.now()
    const response = await searchKnowledge(searchKeyword.value.trim(), 50)
    const endTime = Date.now()
    
    searchResults.value = response.data || []
    searchTime.value = endTime - startTime
    
    if (searchResults.value.length === 0) {
      ElMessage.info('未找到相关结果')
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败')
  } finally {
    searching.value = false
  }
}

// 高级搜索
const handleAdvancedSearch = async () => {
  const hasParams = advancedParams.question || advancedParams.answer || advancedParams.keywords
  if (!hasParams) {
    ElMessage.warning('请至少输入一个搜索条件')
    return
  }

  try {
    searching.value = true
    hasSearched.value = true
    showHot.value = false
    
    const startTime = Date.now()
    const response = await fuzzySearchKnowledge(advancedParams)
    const endTime = Date.now()
    
    searchResults.value = response.data || []
    searchTime.value = endTime - startTime
    
    if (searchResults.value.length === 0) {
      ElMessage.info('未找到相关结果')
    }
  } catch (error) {
    console.error('高级搜索失败:', error)
    ElMessage.error('搜索失败')
  } finally {
    searching.value = false
  }
}

// 重置高级搜索
const resetAdvancedSearch = () => {
  Object.assign(advancedParams, {
    categoryId: null,
    question: '',
    answer: '',
    keywords: ''
  })
}

// 显示热门知识库
const showHotKnowledge = () => {
  showHot.value = true
  hasSearched.value = false
  searchResults.value = []
  searchKeyword.value = ''
  resetAdvancedSearch()
}

// 点击知识库项目
const handleItemClick = async (item) => {
  try {
    // 增加命中次数
    await hitKnowledge(item.knowledgeId)
    
    // 显示详情
    currentItem.value = item
    detailVisible.value = true
    
    // 更新本地命中次数
    item.hitCount = (item.hitCount || 0) + 1
  } catch (error) {
    console.error('记录命中失败:', error)
    // 即使记录失败也显示详情
    currentItem.value = item
    detailVisible.value = true
  }
}

// 截断文本
const truncateText = (text, maxLength) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// 高亮关键词
const highlightKeyword = (text) => {
  if (!text || !searchKeyword.value) return text
  const keyword = searchKeyword.value.trim()
  if (!keyword) return text

  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-container {
  margin-bottom: 20px;
}

.search-main {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.search-input {
  max-width: 600px;
  width: 100%;
}

.search-suggestions {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.suggestions-title {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.suggestion-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.suggestion-tag:hover {
  background-color: #409eff;
  color: white;
}

.advanced-search {
  margin-bottom: 20px;
}

.search-stats {
  margin-bottom: 20px;
  color: #666;
  font-size: 14px;
}

.search-results {
  min-height: 200px;
}

.no-results {
  text-align: center;
  padding: 40px 0;
}

.result-item {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.result-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.result-title {
  margin: 0;
  color: #409eff;
  font-size: 18px;
  font-weight: 600;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.hit-count {
  color: #999;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.result-score {
  color: #67c23a;
  font-size: 12px;
  font-weight: 500;
}

/* 关键词高亮样式 */
:deep(mark) {
  background-color: #fff3cd;
  color: #856404;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 500;
}

.result-question {
  margin-bottom: 8px;
  color: #333;
  line-height: 1.5;
}

.result-answer {
  margin-bottom: 12px;
  color: #666;
  line-height: 1.6;
}

.result-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-tags {
  flex: 1;
}

.result-keywords {
  color: #999;
  font-size: 12px;
}

.keywords-label {
  margin-right: 5px;
}

.hot-knowledge {
  margin-top: 30px;
}

.hot-knowledge h3 {
  margin-bottom: 16px;
  color: #333;
}

.hot-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 12px;
}

.hot-item {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.hot-item:hover {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.hot-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.hot-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.hot-category {
  color: #409eff;
}

.hot-count {
  color: #f56c6c;
}
</style>
