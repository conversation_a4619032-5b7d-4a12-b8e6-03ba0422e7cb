<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="会话ID" prop="sessionId">
        <el-input
          v-model="queryParams.sessionId"
          placeholder="请输入会话ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="对话状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择对话状态" clearable>
          <el-option label="进行中" value="active" />
          <el-option label="已结束" value="completed" />
          <el-option label="已中断" value="interrupted" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['chatbot:dialogue:chat:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['chatbot:dialogue:chat:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="chatList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="会话ID" align="center" prop="sessionId" width="200" />
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="用户昵称" align="center" prop="userNickname" />
      <el-table-column label="对话轮次" align="center" prop="turnCount" />
      <el-table-column label="对话状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="getStatusTag(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="最后消息" align="center" prop="lastMessage" show-overflow-tooltip />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后活跃时间" align="center" prop="lastActiveTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastActiveTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['chatbot:dialogue:chat:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-chat-line-round"
            @click="handleChat(scope.row)"
            v-hasPermi="['chatbot:dialogue:chat:query']"
          >对话</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['chatbot:dialogue:chat:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话详情弹窗 -->
    <el-dialog title="对话详情" :visible.sync="detailOpen" width="80%" append-to-body>
      <div class="chat-detail">
        <div class="chat-info">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="会话ID">{{ currentChat.sessionId }}</el-descriptions-item>
            <el-descriptions-item label="用户ID">{{ currentChat.userId }}</el-descriptions-item>
            <el-descriptions-item label="用户昵称">{{ currentChat.userNickname }}</el-descriptions-item>
            <el-descriptions-item label="对话轮次">{{ currentChat.turnCount }}</el-descriptions-item>
            <el-descriptions-item label="对话状态">
              <el-tag :type="getStatusTag(currentChat.status)">{{ getStatusText(currentChat.status) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ parseTime(currentChat.createTime) }}</el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="chat-messages" style="margin-top: 20px;">
          <h4>对话记录</h4>
          <div class="message-list" v-loading="messageLoading">
            <div 
              v-for="message in messageList" 
              :key="message.messageId"
              :class="['message-item', message.sender === 'user' ? 'user-message' : 'bot-message']"
            >
              <div class="message-header">
                <span class="sender">{{ message.sender === 'user' ? '用户' : '机器人' }}</span>
                <span class="time">{{ parseTime(message.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </div>
              <div class="message-content">{{ message.content }}</div>
              <div class="message-meta" v-if="message.sender === 'bot'">
                <el-tag size="mini" v-if="message.intent">意图: {{ message.intent }}</el-tag>
                <el-tag size="mini" type="info" v-if="message.confidence">置信度: {{ message.confidence }}%</el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 在线对话弹窗 -->
    <el-dialog title="在线对话" :visible.sync="chatOpen" width="60%" append-to-body>
      <div class="online-chat">
        <div class="chat-window">
          <div class="message-display" ref="messageDisplay">
            <div 
              v-for="msg in onlineMessages" 
              :key="msg.id"
              :class="['online-message', msg.sender === 'user' ? 'user' : 'bot']"
            >
              <div class="message-bubble">{{ msg.content }}</div>
              <div class="message-time">{{ parseTime(msg.time, '{h}:{i}:{s}') }}</div>
            </div>
          </div>
          <div class="message-input">
            <el-input
              v-model="inputMessage"
              placeholder="请输入消息..."
              @keyup.enter.native="sendMessage"
            >
              <el-button slot="append" @click="sendMessage" :loading="sending">发送</el-button>
            </el-input>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listChat, getChat, delChat, getChatMessages, sendChatMessage } from "@/api/chatbot/dialogue/chat";

export default {
  name: "DialogueChat",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 对话列表
      chatList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sessionId: null,
        userId: null,
        status: null
      },
      // 详情弹窗
      detailOpen: false,
      // 当前对话
      currentChat: {},
      // 消息列表
      messageList: [],
      // 消息加载状态
      messageLoading: false,
      // 在线对话弹窗
      chatOpen: false,
      // 在线消息
      onlineMessages: [],
      // 输入消息
      inputMessage: '',
      // 发送状态
      sending: false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询对话列表 */
    getList() {
      this.loading = true;
      this.addDateRange(this.queryParams, this.dateRange);
      listChat(this.queryParams).then(response => {
        this.chatList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.sessionId)
      this.multiple = !selection.length
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.currentChat = row;
      this.getChatMessages(row.sessionId);
      this.detailOpen = true;
    },
    /** 获取对话消息 */
    getChatMessages(sessionId) {
      this.messageLoading = true;
      getChatMessages(sessionId).then(response => {
        this.messageList = response.data;
        this.messageLoading = false;
      });
    },
    /** 在线对话 */
    handleChat(row) {
      this.currentChat = row;
      this.onlineMessages = [];
      this.inputMessage = '';
      this.chatOpen = true;
    },
    /** 发送消息 */
    sendMessage() {
      if (!this.inputMessage.trim()) {
        return;
      }
      
      const userMessage = {
        id: Date.now(),
        sender: 'user',
        content: this.inputMessage,
        time: new Date()
      };
      
      this.onlineMessages.push(userMessage);
      
      this.sending = true;
      sendChatMessage({
        sessionId: this.currentChat.sessionId,
        message: this.inputMessage
      }).then(response => {
        const botMessage = {
          id: Date.now() + 1,
          sender: 'bot',
          content: response.data.reply,
          time: new Date()
        };
        this.onlineMessages.push(botMessage);
        this.sending = false;
        this.inputMessage = '';
        
        // 滚动到底部
        this.$nextTick(() => {
          const display = this.$refs.messageDisplay;
          display.scrollTop = display.scrollHeight;
        });
      }).catch(() => {
        this.sending = false;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const sessionIds = row.sessionId || this.ids;
      this.$modal.confirm('是否确认删除会话编号为"' + sessionIds + '"的数据项？').then(function() {
        return delChat(sessionIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('chatbot/dialogue/chat/export', {
        ...this.queryParams
      }, `chat_${new Date().getTime()}.xlsx`)
    },
    /** 获取状态标签 */
    getStatusTag(status) {
      const tagMap = {
        'active': 'success',
        'completed': 'info',
        'interrupted': 'warning'
      };
      return tagMap[status] || 'info';
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const textMap = {
        'active': '进行中',
        'completed': '已结束',
        'interrupted': '已中断'
      };
      return textMap[status] || status;
    }
  }
};
</script>

<style scoped>
.chat-detail {
  max-height: 600px;
  overflow-y: auto;
}

.message-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
}

.message-item {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 8px;
}

.user-message {
  background-color: #e3f2fd;
  margin-left: 20%;
}

.bot-message {
  background-color: #f5f5f5;
  margin-right: 20%;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
  color: #909399;
}

.message-content {
  font-size: 14px;
  line-height: 1.5;
}

.message-meta {
  margin-top: 5px;
}

.online-chat {
  height: 500px;
}

.chat-window {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.message-display {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;
}

.online-message {
  margin-bottom: 10px;
}

.online-message.user {
  text-align: right;
}

.online-message.bot {
  text-align: left;
}

.message-bubble {
  display: inline-block;
  padding: 8px 12px;
  border-radius: 18px;
  max-width: 70%;
  word-wrap: break-word;
}

.online-message.user .message-bubble {
  background-color: #409eff;
  color: white;
}

.online-message.bot .message-bubble {
  background-color: #f0f0f0;
  color: #333;
}

.message-time {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}
</style>
