import { defineStore } from 'pinia';
import { ref } from 'vue';

type ICountStore = {
  count: number;
};

const initState: ICountStore = {
  count: 0,
};

export const useCountStore = defineStore('user', () => {
  const countInfo = ref<ICountStore>({ ...initState });

  const addCount = (count: number = 1) => {
    countInfo.value.count += count;
  };

  return {
    countInfo,
    addCount,
  };
});
