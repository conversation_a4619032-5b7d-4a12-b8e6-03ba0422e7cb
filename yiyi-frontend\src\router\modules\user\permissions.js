/**
 * 用户管理模块权限配置
 * 定义用户管理相关的权限常量和权限检查函数
 */

// 用户管理权限常量
export const USER_PERMISSIONS = {
  // 用户列表权限
  USER_LIST_VIEW: 'user:list:view',
  USER_LIST_ADD: 'user:list:add',
  USER_LIST_EDIT: 'user:list:edit',
  USER_LIST_DELETE: 'user:list:delete',
  USER_LIST_EXPORT: 'user:list:export',
  USER_LIST_IMPORT: 'user:list:import',
  
  // 用户详情权限
  USER_DETAIL_VIEW: 'user:detail:view',
  USER_DETAIL_EDIT: 'user:detail:edit',
  USER_DETAIL_TAG_MANAGE: 'user:detail:tag:manage',
  
  // 用户统计权限
  USER_STATISTICS_VIEW: 'user:statistics:view',
  USER_STATISTICS_EXPORT: 'user:statistics:export',
  
  // 用户状态管理权限
  USER_STATUS_CHANGE: 'user:status:change',
  USER_LEVEL_CHANGE: 'user:level:change'
}

// 权限组配置
export const USER_PERMISSION_GROUPS = {
  // 用户管理员权限组
  USER_ADMIN: [
    USER_PERMISSIONS.USER_LIST_VIEW,
    USER_PERMISSIONS.USER_LIST_ADD,
    USER_PERMISSIONS.USER_LIST_EDIT,
    USER_PERMISSIONS.USER_LIST_DELETE,
    USER_PERMISSIONS.USER_LIST_EXPORT,
    USER_PERMISSIONS.USER_LIST_IMPORT,
    USER_PERMISSIONS.USER_DETAIL_VIEW,
    USER_PERMISSIONS.USER_DETAIL_EDIT,
    USER_PERMISSIONS.USER_DETAIL_TAG_MANAGE,
    USER_PERMISSIONS.USER_STATISTICS_VIEW,
    USER_PERMISSIONS.USER_STATISTICS_EXPORT,
    USER_PERMISSIONS.USER_STATUS_CHANGE,
    USER_PERMISSIONS.USER_LEVEL_CHANGE
  ],
  
  // 用户查看员权限组
  USER_VIEWER: [
    USER_PERMISSIONS.USER_LIST_VIEW,
    USER_PERMISSIONS.USER_DETAIL_VIEW,
    USER_PERMISSIONS.USER_STATISTICS_VIEW
  ],
  
  // 用户分析师权限组
  USER_ANALYST: [
    USER_PERMISSIONS.USER_LIST_VIEW,
    USER_PERMISSIONS.USER_DETAIL_VIEW,
    USER_PERMISSIONS.USER_STATISTICS_VIEW,
    USER_PERMISSIONS.USER_STATISTICS_EXPORT
  ]
}

/**
 * 检查用户是否有指定权限
 * @param {string} permission 权限标识
 * @param {Array} userPermissions 用户权限列表
 * @returns {boolean} 是否有权限
 */
export function hasPermission(permission, userPermissions = []) {
  return userPermissions.includes(permission)
}

/**
 * 检查用户是否有权限组中的任一权限
 * @param {Array} permissions 权限列表
 * @param {Array} userPermissions 用户权限列表
 * @returns {boolean} 是否有权限
 */
export function hasAnyPermission(permissions, userPermissions = []) {
  return permissions.some(permission => userPermissions.includes(permission))
}

/**
 * 检查用户是否有权限组中的所有权限
 * @param {Array} permissions 权限列表
 * @param {Array} userPermissions 用户权限列表
 * @returns {boolean} 是否有权限
 */
export function hasAllPermissions(permissions, userPermissions = []) {
  return permissions.every(permission => userPermissions.includes(permission))
}

export default {
  USER_PERMISSIONS,
  USER_PERMISSION_GROUPS,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions
}
