<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon user-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ totalUsers }}</div>
              <div class="stat-label">总用户数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon active-icon">
              <i class="el-icon-chat-line-round"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ activeUsers }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon satisfaction-icon">
              <i class="el-icon-star-on"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ avgSatisfaction }}</div>
              <div class="stat-label">平均满意度</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon conversation-icon">
              <i class="el-icon-message"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ totalConversations }}</div>
              <div class="stat-label">总对话数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="80px">
        <el-form-item label="用户分群" prop="userGroup">
          <el-select v-model="queryParams.userGroup" placeholder="请选择用户分群" clearable>
            <el-option label="新用户" value="new_user" />
            <el-option label="活跃用户" value="active_user" />
            <el-option label="沉默用户" value="silent_user" />
            <el-option label="流失用户" value="lost_user" />
            <el-option label="VIP用户" value="vip_user" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleDateChange"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <el-button type="success" icon="el-icon-download" size="mini" @click="handleExport">导出报告</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <!-- 用户分群分布 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header" class="clearfix">
            <span>用户分群分布</span>
          </div>
          <div ref="userGroupChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <!-- 用户活跃度趋势 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header" class="clearfix">
            <span>用户活跃度趋势</span>
          </div>
          <div ref="activityTrendChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 满意度分析 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header" class="clearfix">
            <span>用户满意度分析</span>
          </div>
          <div ref="satisfactionChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <!-- 用户兴趣分布 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header" class="clearfix">
            <span>用户兴趣分布</span>
          </div>
          <div ref="interestChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户列表 -->
    <el-card style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>用户详细列表</span>
        <el-button style="float: right;" size="mini" @click="getList">刷新</el-button>
      </div>
      
      <el-table v-loading="loading" :data="userList">
        <el-table-column label="用户ID" align="center" prop="userId" />
        <el-table-column label="用户昵称" align="center" prop="nickname" />
        <el-table-column label="用户分群" align="center" prop="userGroup">
          <template slot-scope="scope">
            <el-tag :type="getUserGroupTag(scope.row.userGroup)">{{ getUserGroupText(scope.row.userGroup) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="活跃度" align="center" prop="activityScore">
          <template slot-scope="scope">
            <el-progress :percentage="scope.row.activityScore * 20" :show-text="false" />
            <span style="margin-left: 10px;">{{ scope.row.activityScore }}/5</span>
          </template>
        </el-table-column>
        <el-table-column label="满意度" align="center" prop="satisfactionScore">
          <template slot-scope="scope">
            <el-rate v-model="scope.row.satisfactionScore" disabled show-score />
          </template>
        </el-table-column>
        <el-table-column label="对话次数" align="center" prop="conversationCount" />
        <el-table-column label="最后活跃时间" align="center" prop="lastActiveTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.lastActiveTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleUserDetail(scope.row)">详情</el-button>
            <el-button size="mini" type="text" @click="handleUserBehavior(scope.row)">行为</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 用户详情弹窗 -->
    <el-dialog title="用户详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户ID">{{ currentUser.userId }}</el-descriptions-item>
        <el-descriptions-item label="用户昵称">{{ currentUser.nickname }}</el-descriptions-item>
        <el-descriptions-item label="用户分群">
          <el-tag :type="getUserGroupTag(currentUser.userGroup)">{{ getUserGroupText(currentUser.userGroup) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="注册时间">{{ parseTime(currentUser.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="活跃度">
          <el-progress :percentage="currentUser.activityScore * 20" />
          <span>{{ currentUser.activityScore }}/5</span>
        </el-descriptions-item>
        <el-descriptions-item label="满意度">
          <el-rate v-model="currentUser.satisfactionScore" disabled show-score />
        </el-descriptions-item>
        <el-descriptions-item label="对话次数">{{ currentUser.conversationCount }}</el-descriptions-item>
        <el-descriptions-item label="最后活跃时间">{{ parseTime(currentUser.lastActiveTime) }}</el-descriptions-item>
        <el-descriptions-item label="用户偏好" :span="2">
          <el-tag 
            v-for="preference in getPreferenceList(currentUser.preferences)" 
            :key="preference" 
            size="mini" 
            style="margin-right: 5px;"
          >{{ preference }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="兴趣标签" :span="2">
          <el-tag 
            v-for="interest in getInterestList(currentUser.interests)" 
            :key="interest" 
            size="mini" 
            type="success"
            style="margin-right: 5px;"
          >{{ interest }}</el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { getUserAnalysis, getUserList, getUserDetail } from "@/api/chatbot/profile/analysis";
import * as echarts from 'echarts';

export default {
  name: "ProfileAnalysis",
  data() {
    return {
      // 统计数据
      totalUsers: 0,
      activeUsers: 0,
      avgSatisfaction: 0,
      totalConversations: 0,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户列表
      userList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userGroup: null
      },
      // 详情弹窗
      detailOpen: false,
      // 当前用户
      currentUser: {},
      // 图表实例
      charts: {}
    };
  },
  created() {
    this.getAnalysisData();
    this.getList();
  },
  mounted() {
    this.initCharts();
  },
  beforeDestroy() {
    // 销毁图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose();
      }
    });
  },
  methods: {
    /** 获取分析数据 */
    getAnalysisData() {
      getUserAnalysis(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        const data = response.data;
        this.totalUsers = data.totalUsers || 0;
        this.activeUsers = data.activeUsers || 0;
        this.avgSatisfaction = data.avgSatisfaction || 0;
        this.totalConversations = data.totalConversations || 0;
        
        // 更新图表数据
        this.updateCharts(data);
      });
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      getUserList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getAnalysisData();
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 日期范围改变 */
    handleDateChange() {
      this.handleQuery();
    },
    /** 用户详情 */
    handleUserDetail(row) {
      getUserDetail(row.userId).then(response => {
        this.currentUser = response.data;
        this.detailOpen = true;
      });
    },
    /** 用户行为 */
    handleUserBehavior(row) {
      this.$router.push("/chatbot/profile/behavior?userId=" + row.userId);
    },
    /** 导出报告 */
    handleExport() {
      this.download('chatbot/profile/analysis/export', {
        ...this.addDateRange(this.queryParams, this.dateRange)
      }, `user_analysis_${new Date().getTime()}.xlsx`)
    },
    /** 初始化图表 */
    initCharts() {
      this.charts.userGroupChart = echarts.init(this.$refs.userGroupChart);
      this.charts.activityTrendChart = echarts.init(this.$refs.activityTrendChart);
      this.charts.satisfactionChart = echarts.init(this.$refs.satisfactionChart);
      this.charts.interestChart = echarts.init(this.$refs.interestChart);
      
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize);
    },
    /** 更新图表 */
    updateCharts(data) {
      // 用户分群分布饼图
      if (this.charts.userGroupChart && data.userGroupData) {
        const option1 = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 10,
            data: data.userGroupData.map(item => item.name)
          },
          series: [
            {
              name: '用户分群',
              type: 'pie',
              radius: ['50%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '18',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: data.userGroupData
            }
          ]
        };
        this.charts.userGroupChart.setOption(option1);
      }
      
      // 用户活跃度趋势图
      if (this.charts.activityTrendChart && data.activityTrendData) {
        const option2 = {
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['活跃用户数', '新增用户数']
          },
          xAxis: {
            type: 'category',
            data: data.activityTrendData.dates
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '活跃用户数',
              type: 'line',
              data: data.activityTrendData.activeUsers
            },
            {
              name: '新增用户数',
              type: 'line',
              data: data.activityTrendData.newUsers
            }
          ]
        };
        this.charts.activityTrendChart.setOption(option2);
      }
      
      // 满意度分析柱状图
      if (this.charts.satisfactionChart && data.satisfactionData) {
        const option3 = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          xAxis: {
            type: 'category',
            data: ['1星', '2星', '3星', '4星', '5星']
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '用户数量',
              type: 'bar',
              data: data.satisfactionData,
              itemStyle: {
                color: function(params) {
                  const colors = ['#ff4757', '#ff6b7a', '#ffa502', '#2ed573', '#1e90ff'];
                  return colors[params.dataIndex];
                }
              }
            }
          ]
        };
        this.charts.satisfactionChart.setOption(option3);
      }
      
      // 用户兴趣分布词云图（简化为柱状图）
      if (this.charts.interestChart && data.interestData) {
        const option4 = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          xAxis: {
            type: 'value'
          },
          yAxis: {
            type: 'category',
            data: data.interestData.map(item => item.name)
          },
          series: [
            {
              name: '用户数量',
              type: 'bar',
              data: data.interestData.map(item => item.value),
              itemStyle: {
                color: '#409eff'
              }
            }
          ]
        };
        this.charts.interestChart.setOption(option4);
      }
    },
    /** 窗口大小变化处理 */
    handleResize() {
      Object.values(this.charts).forEach(chart => {
        if (chart) {
          chart.resize();
        }
      });
    },
    /** 获取用户分群标签 */
    getUserGroupTag(group) {
      const tagMap = {
        'new_user': 'success',
        'active_user': 'primary',
        'silent_user': 'warning',
        'lost_user': 'danger',
        'vip_user': ''
      };
      return tagMap[group] || 'info';
    },
    /** 获取用户分群文本 */
    getUserGroupText(group) {
      const textMap = {
        'new_user': '新用户',
        'active_user': '活跃用户',
        'silent_user': '沉默用户',
        'lost_user': '流失用户',
        'vip_user': 'VIP用户'
      };
      return textMap[group] || group;
    },
    /** 获取偏好列表 */
    getPreferenceList(preferences) {
      if (!preferences) return [];
      try {
        return JSON.parse(preferences);
      } catch (e) {
        return preferences.split(',').filter(item => item.trim());
      }
    },
    /** 获取兴趣列表 */
    getInterestList(interests) {
      if (!interests) return [];
      try {
        return JSON.parse(interests);
      } catch (e) {
        return interests.split(',').filter(item => item.trim());
      }
    }
  }
};
</script>

<style scoped>
.stat-card {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 15px;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.active-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.satisfaction-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.conversation-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.filter-card {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.mb20 {
  margin-bottom: 20px;
}
</style>
