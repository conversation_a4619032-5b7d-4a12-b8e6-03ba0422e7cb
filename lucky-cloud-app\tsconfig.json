{"extends": "@vue/tsconfig/tsconfig.json", "compilerOptions": {"composite": true, "skipLibCheck": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "noImplicitThis": true, "allowSyntheticDefaultImports": true, "allowJs": true, "sourceMap": true, "noImplicitAny": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "outDir": "dist", "lib": ["esnext", "dom"], "types": ["@dcloudio/types", "wot-design-uni/global.d.ts"]}, "vueCompilerOptions": {"target": 3, "nativeTags": ["block", "template", "component", "slot"]}, "exclude": ["node_modules"], "include": ["src/**/*.ts", "src/**/*.js", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.jsx", "src/**/*.vue", "src/**/*.json"]}