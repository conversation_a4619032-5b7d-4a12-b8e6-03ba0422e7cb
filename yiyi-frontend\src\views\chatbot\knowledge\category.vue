<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>知识库分类管理</span>
          <div>
            <el-button
              type="primary"
              plain
              icon="Plus"
              @click="handleAdd"
              v-hasPermi="['chatbot:category:add']"
            >
              新增
            </el-button>
            <el-button type="info" plain icon="Sort" @click="toggleExpandAll">
              展开/折叠
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="分类名称" prop="categoryName">
          <el-input
            v-model="queryParams.categoryName"
            placeholder="请输入分类名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select 
            v-model="queryParams.status" 
            placeholder="请选择状态" 
            clearable 
            style="width: auto; min-width: 100px;"
          >
            <el-option label="启用" value="1" />
            <el-option label="停用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格数据 -->
      <el-table
        v-loading="loading"
        :data="categoryList"
        row-key="categoryId"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column label="分类名称" prop="categoryName">
          <template #default="scope">
            <div class="category-name">
              <el-icon v-if="scope.row.children && scope.row.children.length > 0">
                <Folder />
              </el-icon>
              <el-icon v-else>
                <Document />
              </el-icon>
              <span>{{ scope.row.categoryName }}</span>
              <el-tag v-if="scope.row.knowledgeCount" size="small" type="info" style="margin-left: 8px">
                {{ scope.row.knowledgeCount }}个知识库
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="分类编码" align="center" prop="categoryCode" width="150" />
        <el-table-column label="排序" align="center" prop="sortOrder" width="100" />
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="1"
              inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-button
              type="primary"
              text
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['chatbot:category:edit']"
            >
              修改
            </el-button>
            <el-button
              type="primary"
              text
              icon="Plus"
              @click="handleAdd(scope.row)"
              v-hasPermi="['chatbot:category:add']"
            >
              新增
            </el-button>
            <el-button
              type="danger"
              text
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['chatbot:category:remove']"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加或修改分类对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="categoryRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="上级分类" prop="parentId">
          <el-tree-select
            v-model="form.parentId"
            :data="categoryOptions"
            :props="{ value: 'categoryId', label: 'categoryName', children: 'children' }"
            value-key="categoryId"
            placeholder="选择上级分类"
            check-strictly
          />
        </el-form-item>
        <el-form-item label="分类名称" prop="categoryName">
          <el-input v-model="form.categoryName" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类编码" prop="categoryCode">
          <el-input v-model="form.categoryCode" placeholder="请输入分类编码" />
        </el-form-item>
        <el-form-item label="显示排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="分类状态">
          <el-radio-group v-model="form.status">
            <el-radio value="1">启用</el-radio>
            <el-radio value="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="KnowledgeCategory">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Folder, Document } from '@element-plus/icons-vue'
import {
  listCategory,
  getCategory,
  addCategory,
  updateCategory,
  delCategory,
  getCategoryTree
} from '@/api/chatbot/knowledge.js'
import { parseTime } from '@/utils/ruoyi'
import DictTag from '@/components/DictTag/index.vue'

// 响应式数据
const loading = ref(false)
const showSearch = ref(true)
const categoryList = ref([])
const categoryOptions = ref([])
const open = ref(false)
const title = ref('')
const isExpandAll = ref(false)
const categoryRef = ref()

// 查询参数
const queryParams = reactive({
  categoryName: '',
  status: ''
})

// 表单数据
const form = reactive({
  categoryId: null,
  parentId: 0,
  categoryName: '',
  categoryCode: '',
  sortOrder: 0,
  status: '1',
  remark: ''
})

// 表单验证规则
const rules = reactive({
  categoryName: [{ required: true, message: '分类名称不能为空', trigger: 'blur' }],
  categoryCode: [{ required: true, message: '分类编码不能为空', trigger: 'blur' }],
  sortOrder: [{ required: true, message: '显示排序不能为空', trigger: 'blur' }]
})

// 字典数据
const sys_normal_disable = ref([
  { label: '启用', value: '1', elTagType: 'success' },
  { label: '停用', value: '0', elTagType: 'danger' }
])

// 初始化
onMounted(() => {
  getList()
})

// 获取分类列表
const getList = async () => {
  try {
    loading.value = true
    const response = await getCategoryTree()
    categoryList.value = response.data || []
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分类选项（用于上级分类选择）
const getCategoryOptions = async () => {
  try {
    const response = await getCategoryTree()
    categoryOptions.value = [
      { categoryId: 0, categoryName: '主类目', children: response.data || [] }
    ]
  } catch (error) {
    console.error('获取分类选项失败:', error)
  }
}

// 搜索
const handleQuery = () => {
  getList()
}

// 重置搜索
const resetQuery = () => {
  Object.assign(queryParams, {
    categoryName: '',
    status: ''
  })
  getList()
}

// 展开/折叠
const toggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value
}

// 新增
const handleAdd = async (row) => {
  reset()
  await getCategoryOptions()
  if (row && row.categoryId) {
    form.parentId = row.categoryId
  }
  open.value = true
  title.value = '添加分类'
}

// 修改
const handleUpdate = async (row) => {
  reset()
  await getCategoryOptions()
  try {
    const response = await getCategory(row.categoryId)
    Object.assign(form, response.data)
    open.value = true
    title.value = '修改分类'
  } catch (error) {
    ElMessage.error('获取分类详情失败')
  }
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`是否确认删除名称为"${row.categoryName}"的数据项？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    return delCategory(row.categoryId)
  }).then(() => {
    getList()
    ElMessage.success('删除成功')
  }).catch(() => {})
}

// 状态切换
const handleStatusChange = async (row) => {
  try {
    const text = row.status === '1' ? '启用' : '停用'
    await ElMessageBox.confirm(`确认要${text}该分类吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateCategory(row)
    ElMessage.success(`${text}成功`)
  } catch (error) {
    // 恢复原状态
    row.status = row.status === '1' ? '0' : '1'
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 提交表单
const submitForm = () => {
  categoryRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        if (form.categoryId) {
          await updateCategory(form)
          ElMessage.success('修改成功')
        } else {
          await addCategory(form)
          ElMessage.success('新增成功')
        }
        open.value = false
        getList()
      } catch (error) {
        ElMessage.error(form.categoryId ? '修改失败' : '新增失败')
      }
    }
  })
}

// 取消
const cancel = () => {
  open.value = false
  reset()
}

// 重置表单
const reset = () => {
  Object.assign(form, {
    categoryId: null,
    parentId: 0,
    categoryName: '',
    categoryCode: '',
    sortOrder: 0,
    status: '1',
    remark: ''
  })
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-name .el-icon {
  color: #409eff;
}

:deep(.el-table .el-table__row) {
  transition: background-color 0.3s;
}

:deep(.el-table .el-table__row:hover > td) {
  background-color: #f5f7fa;
}
</style>
