<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>任务详情</span>
        <div style="float: right;">
          <el-button size="mini" @click="goBack">返回</el-button>
          <el-button 
            size="mini" 
            type="primary" 
            @click="handleEdit"
            v-hasPermi="['chatbot:collection:task:edit']"
          >编辑</el-button>
          <el-button 
            size="mini" 
            type="success" 
            @click="handleStart"
            v-hasPermi="['chatbot:collection:task:start']"
            v-if="taskInfo.status == '0'"
          >启动</el-button>
          <el-button 
            size="mini" 
            type="warning" 
            @click="handleStop"
            v-hasPermi="['chatbot:collection:task:stop']"
            v-if="taskInfo.status == '1'"
          >停止</el-button>
        </div>
      </div>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="任务ID">{{ taskInfo.taskId }}</el-descriptions-item>
        <el-descriptions-item label="任务名称">{{ taskInfo.taskName }}</el-descriptions-item>
        <el-descriptions-item label="任务类型">
          <el-tag :type="getTaskTypeTag(taskInfo.taskType)">{{ getTaskTypeText(taskInfo.taskType) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="数据源类型">
          <el-tag :type="getSourceTypeTag(taskInfo.sourceType)">{{ getSourceTypeText(taskInfo.sourceType) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="任务状态">
          <el-tag :type="getStatusTag(taskInfo.status)">{{ getStatusText(taskInfo.status) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="执行频率">{{ taskInfo.cronExpression || '手动执行' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(taskInfo.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(taskInfo.updateTime) }}</el-descriptions-item>
        <el-descriptions-item label="下次执行时间">{{ parseTime(taskInfo.nextExecuteTime) || '无' }}</el-descriptions-item>
        <el-descriptions-item label="最后执行时间">{{ parseTime(taskInfo.lastExecuteTime) || '未执行' }}</el-descriptions-item>
        <el-descriptions-item label="批处理大小">{{ taskInfo.batchSize }}</el-descriptions-item>
        <el-descriptions-item label="最大重试次数">{{ taskInfo.maxRetries }}</el-descriptions-item>
        <el-descriptions-item label="任务描述" :span="2">{{ taskInfo.description || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>数据源配置</span>
      </div>
      <el-descriptions :column="2" border>
        <template v-if="taskInfo.sourceType === 'WEB_CRAWLER'">
          <el-descriptions-item label="目标URL">{{ sourceConfig.url }}</el-descriptions-item>
          <el-descriptions-item label="爬取深度">{{ sourceConfig.depth }}</el-descriptions-item>
          <el-descriptions-item label="请求间隔">{{ sourceConfig.delay }}秒</el-descriptions-item>
          <el-descriptions-item label="CSS选择器">{{ sourceConfig.selector || '无' }}</el-descriptions-item>
        </template>
        <template v-else-if="taskInfo.sourceType === 'API'">
          <el-descriptions-item label="API地址">{{ sourceConfig.apiUrl }}</el-descriptions-item>
          <el-descriptions-item label="请求方法">{{ sourceConfig.method }}</el-descriptions-item>
          <el-descriptions-item label="超时时间">{{ sourceConfig.timeout }}秒</el-descriptions-item>
          <el-descriptions-item label="请求头" :span="2">
            <pre>{{ formatJson(sourceConfig.headers) }}</pre>
          </el-descriptions-item>
          <el-descriptions-item label="请求参数" :span="2">
            <pre>{{ formatJson(sourceConfig.params) }}</pre>
          </el-descriptions-item>
        </template>
        <template v-else-if="taskInfo.sourceType === 'DATABASE'">
          <el-descriptions-item label="数据库类型">{{ sourceConfig.dbType }}</el-descriptions-item>
          <el-descriptions-item label="主机地址">{{ sourceConfig.host }}:{{ sourceConfig.port }}</el-descriptions-item>
          <el-descriptions-item label="数据库名">{{ sourceConfig.database }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ sourceConfig.username }}</el-descriptions-item>
          <el-descriptions-item label="SQL查询" :span="2">
            <pre>{{ sourceConfig.sql }}</pre>
          </el-descriptions-item>
        </template>
        <template v-else-if="taskInfo.sourceType === 'FILE'">
          <el-descriptions-item label="文件路径">{{ sourceConfig.filePath }}</el-descriptions-item>
          <el-descriptions-item label="文件类型">{{ sourceConfig.fileType }}</el-descriptions-item>
          <el-descriptions-item label="编码格式">{{ sourceConfig.encoding }}</el-descriptions-item>
        </template>
      </el-descriptions>
    </el-card>

    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>执行统计</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ taskStats.totalExecutions || 0 }}</div>
            <div class="stat-label">总执行次数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value success">{{ taskStats.successExecutions || 0 }}</div>
            <div class="stat-label">成功次数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value error">{{ taskStats.failedExecutions || 0 }}</div>
            <div class="stat-label">失败次数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ taskStats.totalRecords || 0 }}</div>
            <div class="stat-label">采集记录数</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>执行历史</span>
        <el-button style="float: right;" size="mini" @click="getExecutionHistory">刷新</el-button>
      </div>
      <el-table :data="executionHistory" v-loading="historyLoading">
        <el-table-column label="执行ID" prop="executionId" />
        <el-table-column label="开始时间" prop="startTime">
          <template slot-scope="scope">
            {{ parseTime(scope.row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column label="结束时间" prop="endTime">
          <template slot-scope="scope">
            {{ parseTime(scope.row.endTime) || '执行中' }}
          </template>
        </el-table-column>
        <el-table-column label="执行状态" prop="status">
          <template slot-scope="scope">
            <el-tag :type="getExecutionStatusTag(scope.row.status)">
              {{ getExecutionStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="处理记录数" prop="processedRecords" />
        <el-table-column label="错误信息" prop="errorMessage" show-overflow-tooltip />
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="viewExecutionDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getTask, startTask, stopTask, getTaskStats, getExecutionHistory } from "@/api/chatbot/collection/task";

export default {
  name: "CollectionTaskDetail",
  data() {
    return {
      // 任务信息
      taskInfo: {},
      // 数据源配置
      sourceConfig: {},
      // 任务统计
      taskStats: {},
      // 执行历史
      executionHistory: [],
      // 历史加载状态
      historyLoading: false
    };
  },
  created() {
    const taskId = this.$route.params && this.$route.params.id;
    if (taskId) {
      this.getTaskDetail(taskId);
      this.getTaskStatistics(taskId);
      this.getExecutionHistory(taskId);
    }
  },
  methods: {
    /** 获取任务详情 */
    getTaskDetail(taskId) {
      getTask(taskId).then(response => {
        this.taskInfo = response.data;
        this.sourceConfig = JSON.parse(response.data.sourceConfig || '{}');
      });
    },
    /** 获取任务统计 */
    getTaskStatistics(taskId) {
      getTaskStats(taskId).then(response => {
        this.taskStats = response.data;
      });
    },
    /** 获取执行历史 */
    getExecutionHistory(taskId) {
      this.historyLoading = true;
      getExecutionHistory(taskId || this.taskInfo.taskId).then(response => {
        this.executionHistory = response.rows;
        this.historyLoading = false;
      });
    },
    /** 返回 */
    goBack() {
      this.$router.push("/chatbot/collection/task");
    },
    /** 编辑 */
    handleEdit() {
      this.$router.push("/chatbot/collection/task/edit/" + this.taskInfo.taskId);
    },
    /** 启动任务 */
    handleStart() {
      this.$modal.confirm('是否确认启动任务"' + this.taskInfo.taskName + '"？').then(() => {
        return startTask(this.taskInfo.taskId);
      }).then(() => {
        this.$modal.msgSuccess("启动成功");
        this.getTaskDetail(this.taskInfo.taskId);
      });
    },
    /** 停止任务 */
    handleStop() {
      this.$modal.confirm('是否确认停止任务"' + this.taskInfo.taskName + '"？').then(() => {
        return stopTask(this.taskInfo.taskId);
      }).then(() => {
        this.$modal.msgSuccess("停止成功");
        this.getTaskDetail(this.taskInfo.taskId);
      });
    },
    /** 查看执行详情 */
    viewExecutionDetail(row) {
      this.$router.push("/chatbot/collection/execution/detail/" + row.executionId);
    },
    /** 格式化JSON */
    formatJson(jsonStr) {
      try {
        return JSON.stringify(JSON.parse(jsonStr), null, 2);
      } catch (e) {
        return jsonStr;
      }
    },
    /** 获取任务类型标签 */
    getTaskTypeTag(type) {
      const tagMap = {
        'SCHEDULED': 'success',
        'MANUAL': 'info',
        'TRIGGER': 'warning'
      };
      return tagMap[type] || 'info';
    },
    /** 获取任务类型文本 */
    getTaskTypeText(type) {
      const textMap = {
        'SCHEDULED': '定时任务',
        'MANUAL': '手动任务',
        'TRIGGER': '触发任务'
      };
      return textMap[type] || type;
    },
    /** 获取数据源类型标签 */
    getSourceTypeTag(type) {
      const tagMap = {
        'WEB_CRAWLER': 'primary',
        'API': 'success',
        'DATABASE': 'warning',
        'FILE': 'info'
      };
      return tagMap[type] || 'info';
    },
    /** 获取数据源类型文本 */
    getSourceTypeText(type) {
      const textMap = {
        'WEB_CRAWLER': 'Web爬虫',
        'API': 'API接口',
        'DATABASE': '数据库',
        'FILE': '文件'
      };
      return textMap[type] || type;
    },
    /** 获取状态标签 */
    getStatusTag(status) {
      const tagMap = {
        '0': 'info',
        '1': 'warning',
        '2': 'success',
        '3': 'danger',
        '4': 'info'
      };
      return tagMap[status] || 'info';
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const textMap = {
        '0': '待执行',
        '1': '执行中',
        '2': '已完成',
        '3': '失败',
        '4': '暂停'
      };
      return textMap[status] || status;
    },
    /** 获取执行状态标签 */
    getExecutionStatusTag(status) {
      const tagMap = {
        'RUNNING': 'warning',
        'SUCCESS': 'success',
        'FAILED': 'danger',
        'CANCELLED': 'info'
      };
      return tagMap[status] || 'info';
    },
    /** 获取执行状态文本 */
    getExecutionStatusText(status) {
      const textMap = {
        'RUNNING': '执行中',
        'SUCCESS': '成功',
        'FAILED': '失败',
        'CANCELLED': '已取消'
      };
      return textMap[status] || status;
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.error {
  color: #f56c6c;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
