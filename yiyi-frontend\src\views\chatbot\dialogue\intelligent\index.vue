<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 对话测试区域 -->
      <el-col :span="16">
        <el-card class="chat-card">
          <div slot="header" class="clearfix">
            <span>智能对话测试</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="clearChat">清空对话</el-button>
          </div>
          
          <!-- 对话消息区域 -->
          <div class="chat-messages" ref="chatMessages">
            <div
              v-for="(message, index) in messages"
              :key="index"
              :class="['message-item', message.senderType === '0' ? 'user-message' : 'ai-message']"
            >
              <div class="message-avatar">
                <el-avatar :size="32" :src="message.senderType === '0' ? userAvatar : aiAvatar">
                  {{ message.senderType === '0' ? 'U' : 'AI' }}
                </el-avatar>
              </div>
              <div class="message-content">
                <div class="message-header">
                  <span class="sender-name">{{ message.senderName }}</span>
                  <span class="message-time">{{ formatTime(message.createTime) }}</span>
                </div>
                <div class="message-text">{{ message.content }}</div>
                
                <!-- AI回复的扩展信息 -->
                <div v-if="message.senderType === '1' && message.suggestions" class="message-suggestions">
                  <el-tag
                    v-for="suggestion in message.suggestions"
                    :key="suggestion"
                    size="mini"
                    @click="sendMessage(suggestion)"
                    style="margin-right: 8px; margin-top: 8px; cursor: pointer;"
                  >
                    {{ suggestion }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 输入区域 -->
          <div class="chat-input">
            <el-input
              v-model="inputMessage"
              placeholder="请输入您的问题..."
              @keyup.enter.native="handleSendMessage"
              :disabled="sending"
            >
              <el-button
                slot="append"
                icon="el-icon-s-promotion"
                @click="handleSendMessage"
                :loading="sending"
              >发送</el-button>
            </el-input>
          </div>
        </el-card>
      </el-col>
      
      <!-- 对话状态和分析区域 -->
      <el-col :span="8">
        <el-card class="info-card">
          <div slot="header">
            <span>对话状态</span>
          </div>
          <el-descriptions :column="1" size="small">
            <el-descriptions-item label="会话ID">{{ currentSession.sessionId }}</el-descriptions-item>
            <el-descriptions-item label="当前意图">
              <el-tag v-if="dialogueState.currentIntent" type="primary">
                {{ getIntentName(dialogueState.currentIntent) }}
              </el-tag>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="对话轮次">{{ dialogueState.turnCount || 0 }}</el-descriptions-item>
            <el-descriptions-item label="槽位完整性">
              <el-tag :type="dialogueState.slotsComplete ? 'success' : 'warning'">
                {{ dialogueState.slotsComplete ? '完整' : '不完整' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
          
          <!-- 槽位信息 -->
          <div v-if="dialogueState.slotMap" style="margin-top: 20px;">
            <h4>槽位信息</h4>
            <el-tag
              v-for="(value, key) in dialogueState.slotMap"
              :key="key"
              size="mini"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ key }}: {{ value }}
            </el-tag>
          </div>
        </el-card>
        
        <el-card class="info-card" style="margin-top: 20px;">
          <div slot="header">
            <span>用户画像</span>
          </div>
          <el-descriptions :column="1" size="small">
            <el-descriptions-item label="用户群体">{{ userProfile.userGroup || '-' }}</el-descriptions-item>
            <el-descriptions-item label="活跃度评分">
              <el-rate
                v-model="userProfile.activityScore"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
              />
            </el-descriptions-item>
            <el-descriptions-item label="满意度评分">
              <el-rate
                v-model="userProfile.satisfactionScore"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
              />
            </el-descriptions-item>
            <el-descriptions-item label="旅游偏好">{{ userProfile.travelPreference || '-' }}</el-descriptions-item>
          </el-descriptions>
          
          <!-- 用户偏好标签 -->
          <div v-if="userProfile.preferenceList" style="margin-top: 20px;">
            <h4>偏好标签</h4>
            <el-tag
              v-for="preference in userProfile.preferenceList"
              :key="preference"
              size="mini"
              type="success"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ preference }}
            </el-tag>
          </div>
        </el-card>
        
        <el-card class="info-card" style="margin-top: 20px;">
          <div slot="header">
            <span>快速操作</span>
          </div>
          <el-button-group>
            <el-button size="mini" @click="analyzeUserProfile">分析画像</el-button>
            <el-button size="mini" @click="getRecommendations">获取推荐</el-button>
            <el-button size="mini" @click="resetSession">重置会话</el-button>
          </el-button-group>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 推荐结果对话框 -->
    <el-dialog title="个性化推荐" :visible.sync="recommendationOpen" width="800px" append-to-body>
      <el-table :data="recommendations" max-height="400">
        <el-table-column label="类型" prop="type" width="100">
          <template slot-scope="scope">
            <el-tag :type="getRecommendationTypeTag(scope.row.type)">
              {{ getRecommendationTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="名称" prop="name" :show-overflow-tooltip="true" />
        <el-table-column label="描述" prop="description" :show-overflow-tooltip="true" />
        <el-table-column label="评分" prop="score" width="100">
          <template slot-scope="scope">
            <el-rate
              :value="scope.row.score"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value}"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="askAboutRecommendation(scope.row)">
              询问详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { processChat, getDialogueState, getUserProfile, analyzeUserProfile, getRecommendations } from "@/api/chatbot/dialogue/intelligent";
import { generateUUID } from "@/utils";

export default {
  name: "IntelligentDialogue",
  data() {
    return {
      // 当前会话信息
      currentSession: {
        sessionId: generateUUID(),
        userId: this.$store.state.user.id
      },
      // 对话消息列表
      messages: [],
      // 输入消息
      inputMessage: '',
      // 发送状态
      sending: false,
      // 对话状态
      dialogueState: {},
      // 用户画像
      userProfile: {},
      // 推荐结果
      recommendations: [],
      // 推荐对话框
      recommendationOpen: false,
      // 头像
      userAvatar: '',
      aiAvatar: '',
      // 意图映射
      intentMap: {
        'ATTRACTION_QUERY': '景点查询',
        'FOOD_QUERY': '美食查询',
        'HOTEL_QUERY': '住宿查询',
        'TRANSPORT_QUERY': '交通查询',
        'WEATHER_QUERY': '天气查询',
        'ITINERARY_PLAN': '行程规划',
        'PRICE_QUERY': '价格查询',
        'GREETING': '问候'
      }
    };
  },
  created() {
    this.initChat();
  },
  methods: {
    /** 初始化对话 */
    initChat() {
      // 添加欢迎消息
      this.messages.push({
        senderType: '1',
        senderName: '🤖 智能助手',
        content: '您好！我是北海旅游智能助手，很高兴为您服务！您可以问我关于北海的景点、美食、住宿、交通等问题。',
        createTime: new Date(),
        suggestions: ['北海有什么好玩的地方？', '推荐一些北海特色美食', '北海银滩怎么去？']
      });
      
      // 获取用户画像
      this.loadUserProfile();
    },
    
    /** 发送消息 */
    handleSendMessage() {
      if (!this.inputMessage.trim() || this.sending) {
        return;
      }
      
      this.sendMessage(this.inputMessage);
      this.inputMessage = '';
    },
    
    /** 发送消息到服务器 */
    async sendMessage(message) {
      this.sending = true;
      
      // 添加用户消息
      const userMessage = {
        senderType: '0',
        senderName: '用户',
        content: message,
        createTime: new Date()
      };
      this.messages.push(userMessage);
      
      try {
        // 调用智能对话接口
        const response = await processChat({
          sessionId: this.currentSession.sessionId,
          message: message
        });
        
        // 添加AI回复
        const aiMessage = response.data.aiMessage;
        aiMessage.suggestions = ['继续了解', '更多推荐', '其他问题']; // 示例建议
        this.messages.push(aiMessage);
        
        // 更新对话状态
        this.loadDialogueState();
        
      } catch (error) {
        console.error('发送消息失败:', error);
        this.$modal.msgError('发送消息失败，请重试');
        
        // 添加错误回复
        this.messages.push({
          senderType: '1',
          senderName: '🤖 智能助手',
          content: '抱歉，我暂时无法回答您的问题，请稍后重试。',
          createTime: new Date()
        });
      } finally {
        this.sending = false;
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },
    
    /** 清空对话 */
    clearChat() {
      this.messages = [];
      this.dialogueState = {};
      this.currentSession.sessionId = generateUUID();
      this.initChat();
    },
    
    /** 重置会话 */
    resetSession() {
      this.currentSession.sessionId = generateUUID();
      this.dialogueState = {};
      this.$modal.msgSuccess('会话已重置');
    },
    
    /** 加载对话状态 */
    loadDialogueState() {
      getDialogueState(this.currentSession.sessionId).then(response => {
        this.dialogueState = response.data || {};
      }).catch(error => {
        console.error('获取对话状态失败:', error);
      });
    },
    
    /** 加载用户画像 */
    loadUserProfile() {
      getUserProfile(this.currentSession.userId).then(response => {
        this.userProfile = response.data || {};
      }).catch(error => {
        console.error('获取用户画像失败:', error);
      });
    },
    
    /** 分析用户画像 */
    analyzeUserProfile() {
      analyzeUserProfile(this.currentSession.userId).then(response => {
        this.userProfile = { ...this.userProfile, ...response.data };
        this.$modal.msgSuccess('用户画像分析完成');
      }).catch(error => {
        console.error('分析用户画像失败:', error);
        this.$modal.msgError('分析用户画像失败');
      });
    },
    
    /** 获取推荐 */
    getRecommendations() {
      const requestData = {
        intent: this.dialogueState.currentIntent || 'GENERAL_QUERY',
        entities: this.dialogueState.slotMap || {}
      };
      
      getRecommendations(requestData).then(response => {
        this.recommendations = response.data || [];
        this.recommendationOpen = true;
      }).catch(error => {
        console.error('获取推荐失败:', error);
        this.$modal.msgError('获取推荐失败');
      });
    },
    
    /** 询问推荐详情 */
    askAboutRecommendation(recommendation) {
      const question = `请详细介绍一下${recommendation.name}`;
      this.sendMessage(question);
      this.recommendationOpen = false;
    },
    
    /** 滚动到底部 */
    scrollToBottom() {
      const chatMessages = this.$refs.chatMessages;
      if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
      }
    },
    
    /** 格式化时间 */
    formatTime(time) {
      if (!time) return '';
      const date = new Date(time);
      return date.toLocaleTimeString();
    },
    
    /** 获取意图名称 */
    getIntentName(intent) {
      return this.intentMap[intent] || intent;
    },
    
    /** 获取推荐类型名称 */
    getRecommendationTypeName(type) {
      const typeMap = {
        'ATTRACTION': '景点',
        'FOOD': '美食',
        'HOTEL': '酒店',
        'TRANSPORT': '交通'
      };
      return typeMap[type] || type;
    },
    
    /** 获取推荐类型标签 */
    getRecommendationTypeTag(type) {
      const tagMap = {
        'ATTRACTION': 'primary',
        'FOOD': 'success',
        'HOTEL': 'warning',
        'TRANSPORT': 'info'
      };
      return tagMap[type] || '';
    }
  }
};
</script>

<style scoped>
.chat-card {
  height: 700px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f5f5f5;
}

.message-item {
  display: flex;
  margin-bottom: 20px;
}

.user-message {
  flex-direction: row-reverse;
}

.user-message .message-content {
  background-color: #409eff;
  color: white;
  margin-right: 10px;
}

.ai-message .message-content {
  background-color: white;
  margin-left: 10px;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  max-width: 70%;
  padding: 10px 15px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  font-size: 12px;
  opacity: 0.7;
}

.message-text {
  line-height: 1.5;
}

.message-suggestions {
  margin-top: 10px;
}

.chat-input {
  padding: 20px;
  border-top: 1px solid #ebeef5;
}

.info-card {
  margin-bottom: 20px;
}

.info-card:last-child {
  margin-bottom: 0;
}
</style>
