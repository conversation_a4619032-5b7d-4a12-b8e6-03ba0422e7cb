<template>
  <div class="chatbot-container">
    <!-- 头部信息栏 -->
    <div class="header-info">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="info-card">
            <div class="info-item">
              <div class="info-label">在线用户</div>
              <div class="info-value">{{ onlineCount }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="info-card">
            <div class="info-item">
              <div class="info-label">活跃会话</div>
              <div class="info-value">{{ activeSessions.length }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="info-card">
            <div class="info-item">
              <div class="info-label">连接状态</div>
              <div class="info-value" :class="connectionStatus.class">
                {{ connectionStatus.text }}
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="info-card">
            <div class="info-item">
              <el-button type="primary" @click="createNewSession" :loading="creating">
                <el-icon><Plus /></el-icon>
                新建会话
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="20" style="height: 100%;">
        <!-- 左侧会话列表 -->
        <el-col :span="6">
          <el-card class="session-list-card">
            <template #header>
              <div class="card-header">
                <span>会话列表</span>
                <el-button text @click="refreshSessions" :loading="loading">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            
            <div class="session-list">
              <div 
                v-for="session in activeSessions" 
                :key="session.sessionId"
                class="session-item"
                :class="{ active: currentSessionId === session.sessionId }"
                @click="selectSession(session.sessionId)"
              >
                <div class="session-info">
                  <div class="session-title">{{ session.sessionTitle }}</div>
                  <div class="session-meta">
                    <span class="user-name">{{ session.userName }}</span>
                    <span class="message-count">{{ session.messageCount }}条消息</span>
                  </div>
                  <div class="session-time">{{ formatTime(session.lastMessageTime) }}</div>
                </div>
                <div class="session-actions">
                  <el-button 
                    text 
                    type="danger" 
                    size="small"
                    @click.stop="endSessionConfirm(session.sessionId)"
                  >
                    结束
                  </el-button>
                </div>
              </div>
              
              <el-empty v-if="activeSessions.length === 0" description="暂无活跃会话" />
            </div>
          </el-card>
        </el-col>

        <!-- 右侧聊天区域 -->
        <el-col :span="18">
          <el-card class="chat-card">
            <template #header>
              <div class="card-header">
                <span v-if="currentSession">
                  {{ currentSession.sessionTitle }} - {{ currentSession.userName }}
                </span>
                <span v-else>请选择一个会话开始聊天</span>
              </div>
            </template>

            <div v-if="currentSession" class="chat-container">
              <!-- 消息列表 -->
              <div class="message-list" ref="messageListRef">
                <ChatMessage
                  v-for="message in messages"
                  :key="message.messageId"
                  :message="message"
                  :user-avatar="userAvatar"
                  :bot-avatar="botAvatar"
                />

                <!-- 加载中提示 -->
                <TypingIndicator v-if="isTyping" :bot-avatar="botAvatar" />
              </div>

              <!-- 输入区域 -->
              <div class="input-area">
                <el-input
                  v-model="inputMessage"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入消息..."
                  @keydown.enter.exact="sendMessage"
                  @keydown.enter.shift.exact.prevent
                  :disabled="!isConnected"
                />
                <div class="input-actions">
                  <el-button 
                    type="primary" 
                    @click="sendMessage"
                    :disabled="!inputMessage.trim() || !isConnected"
                  >
                    发送 (Enter)
                  </el-button>
                </div>
              </div>
            </div>

            <div v-else class="empty-chat">
              <el-empty description="请选择一个会话开始聊天" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup name="Chatbot">
import { ref, reactive, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import {
  createSession,
  getActiveSessions,
  getSessionMessages,
  endSession as endSessionApi,
  getOnlineCount
} from '@/api/chatbot/chatbot.js'
import chatWebSocket from '@/utils/websocket'
import useUserStore from '@/store/modules/user'
import { parseTime } from '@/utils/ruoyi'
import ChatMessage from '@/components/ChatMessage/index.vue'
import TypingIndicator from '@/components/TypingIndicator/index.vue'

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const onlineCount = ref(0)
const activeSessions = ref([])
const currentSessionId = ref('')
const currentSession = ref(null)
const messages = ref([])
const inputMessage = ref('')
const isTyping = ref(false)
const isConnected = ref(false)
const messageListRef = ref(null)

// 用户信息
const userStore = useUserStore()
const userAvatar = ref('')
const botAvatar = ref('')

// 连接状态
const connectionStatus = computed(() => {
  if (isConnected.value) {
    return { text: '已连接', class: 'connected' }
  } else {
    return { text: '未连接', class: 'disconnected' }
  }
})

// 初始化
onMounted(async () => {
  await initData()
  await initWebSocket()
})

// 清理
onUnmounted(() => {
  chatWebSocket.close()
})

// 初始化数据
const initData = async () => {
  try {
    loading.value = true
    await Promise.all([
      loadOnlineCount(),
      loadActiveSessions()
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  } finally {
    loading.value = false
  }
}

// 加载在线用户数
const loadOnlineCount = async () => {
  try {
    const response = await getOnlineCount()
    onlineCount.value = response.data || 0
  } catch (error) {
    console.error('获取在线用户数失败:', error)
  }
}

// 加载活跃会话
const loadActiveSessions = async () => {
  try {
    const response = await getActiveSessions()
    activeSessions.value = response.data || []
  } catch (error) {
    console.error('获取活跃会话失败:', error)
    ElMessage.error('获取活跃会话失败')
  }
}

// 刷新会话列表
const refreshSessions = async () => {
  await loadActiveSessions()
  await loadOnlineCount()
}

// 初始化WebSocket
const initWebSocket = async () => {
  try {
    const userId = userStore.id || '1' // 使用当前用户ID
    
    // 连接WebSocket
    await chatWebSocket.connect(userId)
    isConnected.value = true

    // 监听消息
    chatWebSocket.onMessage(handleWebSocketMessage)
    
    // 监听连接状态
    chatWebSocket.onOpen(() => {
      isConnected.value = true
      console.log('WebSocket连接成功')
    })
    
    chatWebSocket.onClose(() => {
      isConnected.value = false
      console.log('WebSocket连接关闭')
    })
    
    chatWebSocket.onError((error) => {
      isConnected.value = false
      console.error('WebSocket连接错误:', error)
    })

  } catch (error) {
    console.error('初始化WebSocket失败:', error)
    ElMessage.error('连接聊天服务失败')
  }
}

// 处理WebSocket消息
const handleWebSocketMessage = (data) => {
  console.log('收到WebSocket消息:', data)
  
  if (data.type === 'message' && data.sessionId === currentSessionId.value) {
    // 添加新消息到当前会话
    messages.value.push({
      messageId: Date.now(), // 临时ID
      sessionId: data.sessionId,
      senderType: data.senderType,
      senderName: data.senderName,
      messageType: data.messageType,
      content: data.content,
      createTime: new Date(data.timestamp).toISOString()
    })
    
    // 滚动到底部
    nextTick(() => {
      scrollToBottom()
    })
    
    // 停止输入提示
    isTyping.value = false
  }
  
  // 刷新会话列表
  refreshSessions()
}

// 创建新会话
const createNewSession = async () => {
  try {
    creating.value = true
    const response = await createSession()
    const sessionId = response.data
    
    ElMessage.success('会话创建成功')
    
    // 刷新会话列表
    await refreshSessions()
    
    // 自动选择新创建的会话
    selectSession(sessionId)
    
  } catch (error) {
    console.error('创建会话失败:', error)
    ElMessage.error('创建会话失败')
  } finally {
    creating.value = false
  }
}

// 选择会话
const selectSession = async (sessionId) => {
  if (currentSessionId.value === sessionId) return
  
  try {
    currentSessionId.value = sessionId
    currentSession.value = activeSessions.value.find(s => s.sessionId === sessionId)
    
    // 加载会话消息
    await loadSessionMessages(sessionId)
    
  } catch (error) {
    console.error('选择会话失败:', error)
    ElMessage.error('加载会话失败')
  }
}

// 加载会话消息
const loadSessionMessages = async (sessionId) => {
  try {
    const response = await getSessionMessages(sessionId)
    messages.value = response.data || []
    
    // 滚动到底部
    nextTick(() => {
      scrollToBottom()
    })
    
  } catch (error) {
    console.error('加载会话消息失败:', error)
    ElMessage.error('加载会话消息失败')
  }
}

// 发送消息
const sendMessage = () => {
  if (!inputMessage.value.trim() || !currentSessionId.value || !isConnected.value) {
    return
  }
  
  const message = {
    sessionId: currentSessionId.value,
    content: inputMessage.value.trim(),
    messageType: '1'
  }
  
  // 发送WebSocket消息
  const success = chatWebSocket.sendMessage(message)
  
  if (success) {
    // 添加用户消息到界面
    messages.value.push({
      messageId: Date.now(),
      sessionId: currentSessionId.value,
      senderType: '0',
      senderName: userStore.name || '用户',
      messageType: '1',
      content: inputMessage.value.trim(),
      createTime: new Date().toISOString()
    })
    
    // 清空输入框
    inputMessage.value = ''
    
    // 显示输入提示
    isTyping.value = true
    
    // 滚动到底部
    nextTick(() => {
      scrollToBottom()
    })
  }
}

// 结束会话确认
const endSessionConfirm = (sessionId) => {
  ElMessageBox.confirm(
    '确定要结束这个会话吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    endSessionAction(sessionId)
  })
}

// 结束会话
const endSessionAction = async (sessionId) => {
  try {
    await endSessionApi(sessionId)
    ElMessage.success('会话已结束')

    // 如果结束的是当前会话，清空聊天区域
    if (currentSessionId.value === sessionId) {
      currentSessionId.value = ''
      currentSession.value = null
      messages.value = []
    }

    // 刷新会话列表
    await refreshSessions()

  } catch (error) {
    console.error('结束会话失败:', error)
    ElMessage.error('结束会话失败')
  }
}

// 滚动到底部
const scrollToBottom = () => {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

// 格式化时间
const formatTime = (time) => {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}
</script>

<style scoped>
.chatbot-container {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.header-info {
  margin-bottom: 20px;
}

.info-card {
  height: 80px;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.info-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.info-value.connected {
  color: #67c23a;
}

.info-value.disconnected {
  color: #f56c6c;
}

.main-content {
  flex: 1;
  overflow: hidden;
}

.session-list-card,
.chat-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.session-list {
  height: calc(100% - 60px);
  overflow-y: auto;
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.session-item:hover {
  background-color: #f5f7fa;
}

.session-item.active {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.session-info {
  flex: 1;
}

.session-title {
  font-weight: bold;
  margin-bottom: 4px;
  color: #333;
}

.session-meta {
  display: flex;
  gap: 10px;
  margin-bottom: 4px;
}

.user-name {
  color: #666;
  font-size: 12px;
}

.message-count {
  color: #999;
  font-size: 12px;
}

.session-time {
  color: #999;
  font-size: 12px;
}

.chat-container {
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f8f9fa;
}

/* 消息相关样式已移至组件中 */

.input-area {
  padding: 20px;
  border-top: 1px solid #e8e8e8;
  background: white;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.empty-chat {
  height: calc(100% - 60px);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 滚动条样式 */
.session-list::-webkit-scrollbar,
.message-list::-webkit-scrollbar {
  width: 6px;
}

.session-list::-webkit-scrollbar-track,
.message-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.session-list::-webkit-scrollbar-thumb,
.message-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.session-list::-webkit-scrollbar-thumb:hover,
.message-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
