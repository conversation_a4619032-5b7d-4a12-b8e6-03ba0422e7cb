<template>
  <div class="user-overview">
    <!-- 用户统计卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-item">
            <div class="overview-icon total">
              <el-icon><User /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-number">{{ overviewData.totalUsers || 0 }}</div>
              <div class="overview-label">用户总数</div>
              <div class="overview-change" :class="overviewData.totalUsersChange >= 0 ? 'positive' : 'negative'">
                <el-icon>
                  <CaretTop v-if="overviewData.totalUsersChange >= 0" />
                  <CaretBottom v-else />
                </el-icon>
                {{ Math.abs(overviewData.totalUsersChange || 0) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-item">
            <div class="overview-icon today">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-number">{{ overviewData.todayNewUsers || 0 }}</div>
              <div class="overview-label">今日新增</div>
              <div class="overview-change" :class="overviewData.todayNewUsersChange >= 0 ? 'positive' : 'negative'">
                <el-icon>
                  <CaretTop v-if="overviewData.todayNewUsersChange >= 0" />
                  <CaretBottom v-else />
                </el-icon>
                {{ Math.abs(overviewData.todayNewUsersChange || 0) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-item">
            <div class="overview-icon active">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-number">{{ overviewData.activeUsers || 0 }}</div>
              <div class="overview-label">活跃用户</div>
              <div class="overview-change" :class="overviewData.activeUsersChange >= 0 ? 'positive' : 'negative'">
                <el-icon>
                  <CaretTop v-if="overviewData.activeUsersChange >= 0" />
                  <CaretBottom v-else />
                </el-icon>
                {{ Math.abs(overviewData.activeUsersChange || 0) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-item">
            <div class="overview-icon growth">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-number">{{ overviewData.growthRate || 0 }}%</div>
              <div class="overview-label">增长率</div>
              <div class="overview-change" :class="overviewData.growthRateChange >= 0 ? 'positive' : 'negative'">
                <el-icon>
                  <CaretTop v-if="overviewData.growthRateChange >= 0" />
                  <CaretBottom v-else />
                </el-icon>
                {{ Math.abs(overviewData.growthRateChange || 0) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户增长趋势图 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>用户增长趋势</span>
          <div>
            <el-radio-group v-model="chartPeriod" @change="loadChartData" size="small">
              <el-radio-button value="7">近7天</el-radio-button>
              <el-radio-button value="30">近30天</el-radio-button>
              <el-radio-button value="90">近90天</el-radio-button>
            </el-radio-group>
            <el-button type="primary" size="small" @click="refresh" style="margin-left: 10px;">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      <div ref="chartRef" style="height: 300px;" v-loading="chartLoading"></div>
    </el-card>

    <!-- 用户分布统计 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 用户等级分布 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>用户等级分布</span>
          </template>
          <div class="level-distribution">
            <div 
              v-for="level in levelDistribution" 
              :key="level.name"
              class="level-item"
            >
              <div class="level-info">
                <div class="level-name">{{ level.name }}</div>
                <div class="level-count">{{ level.count }}人</div>
              </div>
              <div class="level-progress">
                <el-progress 
                  :percentage="level.percentage" 
                  :color="level.color"
                  :show-text="false"
                />
              </div>
              <div class="level-percentage">{{ level.percentage }}%</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 用户地域分布 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>用户地域分布</span>
          </template>
          <div class="region-distribution">
            <div 
              v-for="region in regionDistribution" 
              :key="region.name"
              class="region-item"
            >
              <div class="region-name">{{ region.name }}</div>
              <div class="region-count">{{ region.count }}</div>
              <div class="region-bar">
                <div 
                  class="region-fill" 
                  :style="{ width: region.percentage + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 最新注册用户 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最新注册用户</span>
              <el-button text @click="loadLatestUsers">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="latest-users" v-loading="usersLoading">
            <div 
              v-for="user in latestUsers" 
              :key="user.userId"
              class="user-item"
            >
              <el-avatar :size="32" :src="user.avatar">
                {{ user.nickname?.charAt(0) }}
              </el-avatar>
              <div class="user-info">
                <div class="user-name">{{ user.nickname }}</div>
                <div class="user-time">{{ formatTime(user.registerTime) }}</div>
              </div>
              <el-tag :type="getLevelType(user.userLevel)" size="small">
                {{ getLevelText(user.userLevel) }}
              </el-tag>
            </div>
            <el-empty v-if="latestUsers.length === 0" description="暂无数据" :image-size="60" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  User, 
  UserFilled, 
  Connection, 
  TrendCharts, 
  Refresh,
  CaretTop,
  CaretBottom
} from '@element-plus/icons-vue'
import { getUserOverview, getUserGrowthData } from '@/api/chatbot/dashboard.js'
import { getUserList } from '@/api/chatbot/user.js'
import * as echarts from 'echarts'

// Props
const props = defineProps({
  refreshInterval: {
    type: Number,
    default: 30000 // 30秒
  }
})

// 响应式数据
const chartLoading = ref(false)
const usersLoading = ref(false)
const chartPeriod = ref('30')

const overviewData = reactive({
  totalUsers: 0,
  totalUsersChange: 0,
  todayNewUsers: 0,
  todayNewUsersChange: 0,
  activeUsers: 0,
  activeUsersChange: 0,
  growthRate: 0,
  growthRateChange: 0
})

const levelDistribution = ref([
  { name: '普通用户', count: 0, percentage: 0, color: '#909399' },
  { name: 'VIP用户', count: 0, percentage: 0, color: '#e6a23c' },
  { name: 'SVIP用户', count: 0, percentage: 0, color: '#f56c6c' }
])

const regionDistribution = ref([])
const latestUsers = ref([])

// 图表引用
const chartRef = ref()
let chart = null
let refreshTimer = null

// 初始化
onMounted(() => {
  loadOverviewData()
  loadLatestUsers()
  nextTick(() => {
    initChart()
    startAutoRefresh()
  })
})

// 清理
onUnmounted(() => {
  if (chart) chart.dispose()
  if (refreshTimer) clearInterval(refreshTimer)
})

// 加载概览数据
const loadOverviewData = async () => {
  try {
    const response = await getUserOverview()
    Object.assign(overviewData, response.data)
    
    // 更新等级分布
    if (response.data.levelDistribution) {
      levelDistribution.value = response.data.levelDistribution
    }
    
    // 更新地域分布
    if (response.data.regionDistribution) {
      regionDistribution.value = response.data.regionDistribution
    }
  } catch (error) {
    console.error('获取用户概览数据失败:', error)
  }
}

// 加载最新用户
const loadLatestUsers = async () => {
  try {
    usersLoading.value = true
    const response = await getUserList({
      pageNum: 1,
      pageSize: 8,
      orderByColumn: 'registerTime',
      isAsc: 'desc'
    })
    latestUsers.value = response.rows || []
  } catch (error) {
    console.error('获取最新用户失败:', error)
  } finally {
    usersLoading.value = false
  }
}

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  await loadChartData()
  
  window.addEventListener('resize', handleResize)
}

// 加载图表数据
const loadChartData = async () => {
  try {
    chartLoading.value = true
    const response = await getUserGrowthData({ period: chartPeriod.value })
    const data = response.data || []
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['新增用户', '累计用户'],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.date),
        axisPointer: {
          type: 'shadow'
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '新增用户',
          position: 'left'
        },
        {
          type: 'value',
          name: '累计用户',
          position: 'right'
        }
      ],
      series: [
        {
          name: '新增用户',
          type: 'bar',
          data: data.map(item => item.newUsers),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          }
        },
        {
          name: '累计用户',
          type: 'line',
          yAxisIndex: 1,
          data: data.map(item => item.totalUsers),
          smooth: true,
          itemStyle: { color: '#67c23a' },
          lineStyle: { width: 3 }
        }
      ]
    }
    
    chart.setOption(option)
  } catch (error) {
    console.error('加载图表数据失败:', error)
  } finally {
    chartLoading.value = false
  }
}

// 开始自动刷新
const startAutoRefresh = () => {
  if (props.refreshInterval > 0) {
    refreshTimer = setInterval(() => {
      loadOverviewData()
    }, props.refreshInterval)
  }
}

// 手动刷新
const refresh = () => {
  loadOverviewData()
  loadChartData()
  loadLatestUsers()
  ElMessage.success('数据已刷新')
}

// 处理窗口大小变化
const handleResize = () => {
  if (chart) chart.resize()
}

// 工具函数
const formatTime = (time) => {
  const now = new Date()
  const target = new Date(time)
  const diff = now - target
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'
  if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'
  return Math.floor(diff / 86400000) + '天前'
}

const getLevelType = (level) => {
  const types = { 1: '', 2: 'warning', 3: 'danger' }
  return types[level] || ''
}

const getLevelText = (level) => {
  const texts = { 1: '普通', 2: 'VIP', 3: 'SVIP' }
  return texts[level] || '普通'
}

// 暴露刷新方法
defineExpose({
  refresh
})
</script>

<style scoped>
.user-overview {
  width: 100%;
}

.overview-card {
  height: 120px;
}

.overview-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.overview-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.overview-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.overview-icon.today {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.overview-icon.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.overview-icon.growth {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.overview-content {
  flex: 1;
}

.overview-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.overview-change {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 12px;
  font-weight: 500;
}

.overview-change.positive {
  color: #67c23a;
}

.overview-change.negative {
  color: #f56c6c;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 等级分布样式 */
.level-distribution {
  padding: 10px 0;
}

.level-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.level-item:last-child {
  margin-bottom: 0;
}

.level-info {
  width: 80px;
  margin-right: 12px;
}

.level-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
}

.level-count {
  font-size: 12px;
  color: #666;
}

.level-progress {
  flex: 1;
  margin-right: 12px;
}

.level-percentage {
  width: 40px;
  text-align: right;
  font-size: 12px;
  color: #666;
}

/* 地域分布样式 */
.region-distribution {
  padding: 10px 0;
  max-height: 240px;
  overflow-y: auto;
}

.region-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.region-item:last-child {
  margin-bottom: 0;
}

.region-name {
  width: 60px;
  font-size: 14px;
  color: #333;
  margin-right: 12px;
}

.region-count {
  width: 40px;
  font-size: 12px;
  color: #666;
  margin-right: 12px;
}

.region-bar {
  flex: 1;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.region-fill {
  height: 100%;
  background: linear-gradient(90deg, #409eff, #67c23a);
  transition: width 0.3s;
}

/* 最新用户样式 */
.latest-users {
  max-height: 240px;
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.user-item:last-child {
  border-bottom: none;
}

.user-info {
  flex: 1;
  margin-left: 12px;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-time {
  font-size: 12px;
  color: #999;
}
</style>
