<template>
  <div class="app-container">
    <!-- 用户基本信息 -->
    <el-card class="user-info-card">
      <template #header>
        <div class="card-header">
          <span>用户基本信息</span>
          <div>
            <el-button @click="$router.go(-1)">
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <el-button type="primary" @click="handleEdit">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </div>
        </div>
      </template>

      <div v-loading="loading">
        <el-row :gutter="20" v-if="userInfo">
          <el-col :span="6">
            <div class="user-avatar-section">
              <el-avatar :size="120" :src="userInfo.avatar">
                {{ userInfo.nickname?.charAt(0) }}
              </el-avatar>
              <div class="user-basic">
                <h3>{{ userInfo.nickname }}</h3>
                <el-tag :type="getLevelType(userInfo.userLevel)" size="large">
                  {{ getLevelText(userInfo.userLevel) }}
                </el-tag>
              </div>
            </div>
          </el-col>
          
          <el-col :span="18">
            <el-descriptions :column="3" border>
              <el-descriptions-item label="用户ID">{{ userInfo.userId }}</el-descriptions-item>
              <el-descriptions-item label="手机号">{{ userInfo.phoneNumber }}</el-descriptions-item>
              <el-descriptions-item label="邮箱">{{ userInfo.email || '未设置' }}</el-descriptions-item>
              <el-descriptions-item label="性别">
                <el-tag :type="getGenderType(userInfo.gender)" size="small">
                  {{ getGenderText(userInfo.gender) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="年龄段">{{ getAgeGroupText(userInfo.ageGroup) }}</el-descriptions-item>
              <el-descriptions-item label="城市">{{ userInfo.city || '未设置' }}</el-descriptions-item>
              <el-descriptions-item label="旅行偏好">{{ userInfo.travelPreference || '未设置' }}</el-descriptions-item>
              <el-descriptions-item label="总对话次数">{{ userInfo.totalChats || 0 }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="userInfo.status === '0' ? 'success' : 'danger'" size="small">
                  {{ userInfo.status === '0' ? '正常' : '停用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="注册时间">{{ parseTime(userInfo.registerTime) }}</el-descriptions-item>
              <el-descriptions-item label="最后登录">{{ parseTime(userInfo.loginDate) }}</el-descriptions-item>
              <el-descriptions-item label="登录IP">{{ userInfo.loginIp || '未知' }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 用户标签管理 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>用户标签</span>
          <el-button type="primary" size="small" @click="handleEditTags">
            <el-icon><Edit /></el-icon>
            编辑标签
          </el-button>
        </div>
      </template>
      
      <div class="user-tags">
        <el-tag
          v-for="tag in userTags"
          :key="tag"
          closable
          @close="removeTag(tag)"
          style="margin-right: 8px; margin-bottom: 8px;"
        >
          {{ tag }}
        </el-tag>
        <el-input
          v-if="inputVisible"
          ref="inputRef"
          v-model="inputValue"
          size="small"
          style="width: 100px;"
          @keyup.enter="handleInputConfirm"
          @blur="handleInputConfirm"
        />
        <el-button v-else size="small" @click="showInput">
          <el-icon><Plus /></el-icon>
          添加标签
        </el-button>
      </div>
    </el-card>

    <!-- 数据统计 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>对话历史</span>
          </template>
          <div class="chat-history" v-loading="chatLoading">
            <div 
              v-for="chat in chatHistory" 
              :key="chat.sessionId"
              class="chat-item"
            >
              <div class="chat-info">
                <div class="chat-title">会话 {{ chat.sessionId }}</div>
                <div class="chat-meta">
                  <span>{{ chat.messageCount }}条消息</span>
                  <span>{{ parseTime(chat.createTime) }}</span>
                </div>
              </div>
              <el-button type="primary" text size="small">查看详情</el-button>
            </div>
            <el-empty v-if="chatHistory.length === 0" description="暂无对话记录" :image-size="60" />
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>行为记录</span>
          </template>
          <div class="behavior-records" v-loading="behaviorLoading">
            <div 
              v-for="record in behaviorRecords" 
              :key="record.behaviorId"
              class="behavior-item"
            >
              <div class="behavior-info">
                <div class="behavior-type">{{ getBehaviorTypeText(record.behaviorType) }}</div>
                <div class="behavior-time">{{ parseTime(record.behaviorTime) }}</div>
              </div>
              <div class="behavior-content">{{ record.behaviorContent }}</div>
            </div>
            <el-empty v-if="behaviorRecords.length === 0" description="暂无行为记录" :image-size="60" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 编辑用户信息对话框 -->
    <el-dialog title="编辑用户信息" v-model="editVisible" width="600px" append-to-body>
      <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="100px">
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="editForm.nickname" placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="editForm.gender">
            <el-radio value="0">男</el-radio>
            <el-radio value="1">女</el-radio>
            <el-radio value="2">未知</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="城市" prop="city">
          <el-input v-model="editForm.city" placeholder="请输入城市" />
        </el-form-item>
        <el-form-item label="用户等级" prop="userLevel">
          <el-select v-model="editForm.userLevel" placeholder="请选择用户等级">
            <el-option label="普通用户" :value="1" />
            <el-option label="VIP用户" :value="2" />
            <el-option label="SVIP用户" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="旅行偏好" prop="travelPreference">
          <el-input v-model="editForm.travelPreference" type="textarea" placeholder="请输入旅行偏好" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitEdit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="UserDetail">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Edit, Plus } from '@element-plus/icons-vue'
import { 
  getUserDetail, 
  updateUser, 
  updateUserTags,
  getUserChatHistory,
  getUserBehaviorRecords
} from '@/api/chatbot/user.js'
import { parseTime } from '@/utils/ruoyi'

// 路由
const route = useRoute()
const userId = route.params.id

// 响应式数据
const loading = ref(false)
const chatLoading = ref(false)
const behaviorLoading = ref(false)
const userInfo = ref(null)
const chatHistory = ref([])
const behaviorRecords = ref([])
const userTags = ref([])
const editVisible = ref(false)
const inputVisible = ref(false)
const inputValue = ref('')
const inputRef = ref()

// 编辑表单
const editForm = reactive({
  nickname: '',
  email: '',
  gender: '',
  city: '',
  userLevel: 1,
  travelPreference: ''
})

const editRules = reactive({
  nickname: [{ required: true, message: '请输入昵称', trigger: 'blur' }]
})

// 初始化
onMounted(() => {
  loadUserDetail()
  loadChatHistory()
  loadBehaviorRecords()
})

// 加载用户详情
const loadUserDetail = async () => {
  try {
    loading.value = true
    const response = await getUserDetail(userId)
    userInfo.value = response.data
    
    // 解析用户标签
    if (userInfo.value.userTags) {
      try {
        userTags.value = JSON.parse(userInfo.value.userTags)
      } catch {
        userTags.value = []
      }
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  } finally {
    loading.value = false
  }
}

// 加载对话历史
const loadChatHistory = async () => {
  try {
    chatLoading.value = true
    const response = await getUserChatHistory(userId, { pageNum: 1, pageSize: 10 })
    chatHistory.value = response.rows || []
  } catch (error) {
    console.error('获取对话历史失败:', error)
  } finally {
    chatLoading.value = false
  }
}

// 加载行为记录
const loadBehaviorRecords = async () => {
  try {
    behaviorLoading.value = true
    const response = await getUserBehaviorRecords(userId, { pageNum: 1, pageSize: 10 })
    behaviorRecords.value = response.rows || []
  } catch (error) {
    console.error('获取行为记录失败:', error)
  } finally {
    behaviorLoading.value = false
  }
}

// 编辑用户信息
const handleEdit = () => {
  Object.assign(editForm, {
    nickname: userInfo.value.nickname,
    email: userInfo.value.email,
    gender: userInfo.value.gender,
    city: userInfo.value.city,
    userLevel: userInfo.value.userLevel,
    travelPreference: userInfo.value.travelPreference
  })
  editVisible.value = true
}

// 提交编辑
const submitEdit = async () => {
  try {
    await updateUser(userId, editForm)
    ElMessage.success('更新成功')
    editVisible.value = false
    loadUserDetail()
  } catch (error) {
    ElMessage.error('更新失败')
  }
}

// 显示输入框
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

// 确认输入
const handleInputConfirm = () => {
  if (inputValue.value && !userTags.value.includes(inputValue.value)) {
    userTags.value.push(inputValue.value)
    updateTags()
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 移除标签
const removeTag = (tag) => {
  userTags.value = userTags.value.filter(t => t !== tag)
  updateTags()
}

// 更新标签
const updateTags = async () => {
  try {
    await updateUserTags(userId, userTags.value)
    ElMessage.success('标签更新成功')
  } catch (error) {
    ElMessage.error('标签更新失败')
  }
}

// 工具函数
const getGenderType = (gender) => {
  const types = { '0': 'primary', '1': 'danger', '2': 'info' }
  return types[gender] || 'info'
}

const getGenderText = (gender) => {
  const texts = { '0': '男', '1': '女', '2': '未知' }
  return texts[gender] || '未知'
}

const getLevelType = (level) => {
  const types = { 1: '', 2: 'warning', 3: 'danger' }
  return types[level] || ''
}

const getLevelText = (level) => {
  const texts = { 1: '普通用户', 2: 'VIP用户', 3: 'SVIP用户' }
  return texts[level] || '普通用户'
}

const getAgeGroupText = (ageGroup) => {
  const texts = { 1: '18-25岁', 2: '26-35岁', 3: '36-45岁', 4: '46-55岁', 5: '55岁以上' }
  return texts[ageGroup] || '未知'
}

const getBehaviorTypeText = (type) => {
  const texts = {
    'search': '搜索知识库',
    'chat': '发起对话',
    'view': '查看详情',
    'feedback': '反馈评价'
  }
  return texts[type] || type
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-avatar-section {
  text-align: center;
  padding: 20px;
}

.user-basic {
  margin-top: 16px;
}

.user-basic h3 {
  margin: 8px 0;
  color: #333;
}

.user-tags {
  min-height: 40px;
}

.chat-history,
.behavior-records {
  max-height: 400px;
  overflow-y: auto;
}

.chat-item,
.behavior-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.chat-item:last-child,
.behavior-item:last-child {
  border-bottom: none;
}

.chat-info,
.behavior-info {
  flex: 1;
}

.chat-title,
.behavior-type {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.chat-meta,
.behavior-time {
  font-size: 12px;
  color: #999;
}

.chat-meta span {
  margin-right: 12px;
}

.behavior-content {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
