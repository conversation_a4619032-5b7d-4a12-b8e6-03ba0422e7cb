# 🎊 幸运大转盘抽奖程序

## 项目简介

这是一个基于 uni-app + Vue3 + TypeScript + Pinia 开发的幸运大转盘抽奖程序，具有精美的中国风喜庆界面设计和完整的抽奖功能。

## 功能特性

### 🎯 核心功能
- **转盘抽奖**: 支持多种奖品的转盘抽奖，带有流畅的转动动画
- **奖品管理**: 支持设置奖品级别、名称、概率、图标等信息
- **用户系统**: 模拟用户信息，包含用户ID、昵称、手机号等
- **抽奖限制**: 支持设置每用户最大抽奖次数
- **结果展示**: 精美的中奖结果模态框，符合中国喜庆色彩审美

### 🎨 界面设计
- **中国风配色**: 采用红色、金色等喜庆色彩
- **动画效果**: 转盘转动、烟花特效、浮动元素等
- **响应式布局**: 适配不同屏幕尺寸
- **组件化设计**: 可复用的转盘和模态框组件

### 🏗️ 技术架构
- **分层架构**: API层、Store层、组件层分离
- **TypeScript**: 完整的类型定义和类型安全
- **Pinia状态管理**: 统一的状态管理方案
- **模拟API**: 完整的模拟后端接口

## 项目结构

```
src/
├── types/
│   └── lottery.ts          # 抽奖相关类型定义
├── api/
│   └── lottery.ts          # 抽奖API接口（模拟）
├── store/
│   └── lottery.ts          # 抽奖状态管理
├── components/
│   ├── LotteryWheel.vue    # 转盘组件
│   └── LotteryResultModal.vue # 结果模态框组件
└── pages/
    └── lottery/
        └── index.vue       # 抽奖页面
```

## 使用说明

### 启动项目
```bash
cd lucky-cloud-app
yarn install
yarn start
```

### 访问抽奖页面
1. 启动项目后访问 http://localhost:5173/
2. 点击首页的"🎊 幸运大转盘 🎊"按钮
3. 或直接访问 http://localhost:5173/#/pages/lottery/index

### 抽奖流程
1. 页面加载时自动初始化抽奖数据
2. 显示用户信息和剩余抽奖次数
3. 点击转盘中心的"开始抽奖"按钮
4. 转盘开始转动，模拟网络请求（2秒延迟）
5. 转盘停止，显示中奖结果模态框
6. 可选择继续抽奖或关闭

## 数据配置

### 奖品配置
在 `src/api/lottery.ts` 中的 `mockPrizes` 数组中配置奖品：

```typescript
{
  id: 'prize_001',           // 奖品ID
  name: 'iPhone 15 Pro',     // 奖品名称
  level: PrizeLevel.FIRST,   // 奖品级别
  probability: 0.01,         // 中奖概率 (1%)
  icon: '📱',               // 奖品图标
  description: '苹果最新旗舰手机',
  value: 8999,              // 奖品价值
  stock: 2                  // 库存数量
}
```

### 用户配置
在 `src/api/lottery.ts` 中的 `mockUser` 对象中配置用户信息：

```typescript
{
  id: 'user_001',
  username: 'lucky_user',
  nickname: '幸运儿',
  phone: '138****8888',
  avatar: '👤'
}
```

### 抽奖配置
在 `src/api/lottery.ts` 中的 `mockLotteryConfig` 对象中配置抽奖规则：

```typescript
{
  id: 'activity_001',
  name: '新年幸运大转盘',
  maxDrawCount: 3,          // 最大抽奖次数
  isActive: true            // 是否激活
}
```

## 控制台输出

每次抽奖完成后，程序会在浏览器控制台输出模拟的后端返回数据：

```json
{
  "drawResult": {
    "drawId": "draw_1703123456789_abc123def",
    "timestamp": "2023/12/21 10:30:45",
    "user": {
      "userId": "user_001",
      "username": "lucky_user",
      "nickname": "幸运儿",
      "phone": "138****8888"
    },
    "prize": {
      "prizeId": "prize_001",
      "prizeName": "iPhone 15 Pro",
      "prizeLevel": 1,
      "prizeIcon": "📱",
      "prizeValue": 8999
    },
    "success": true,
    "message": "恭喜您获得iPhone 15 Pro！"
  }
}
```

## 后续扩展

### 接入真实后端
1. 替换 `src/api/lottery.ts` 中的模拟接口
2. 修改请求地址和参数格式
3. 处理真实的错误响应

### 功能扩展
- 添加抽奖历史记录页面
- 支持多种抽奖活动切换
- 添加分享功能
- 支持实时库存更新
- 添加音效和更多动画

### 部署说明
- 支持打包为H5、小程序、App等多端应用
- 使用 `yarn build` 进行生产环境打包

## 技术支持

如有问题，请检查：
1. Node.js 版本是否符合要求
2. 依赖是否正确安装
3. 浏览器控制台是否有错误信息
4. 网络请求是否正常

---

🎉 祝您使用愉快，好运连连！
