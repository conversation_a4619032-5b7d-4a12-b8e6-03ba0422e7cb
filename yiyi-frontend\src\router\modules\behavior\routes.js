/**
 * 行为分析路由详细配置
 * 定义行为分析模块的所有路由规则和权限
 */

export const behaviorMonitorRoute = {
  path: 'behavior/monitor',
  component: () => import('@/views/behavior/monitor/index.vue'),
  name: 'BehaviorMonitor',
  meta: {
    title: '行为监控',
    icon: 'monitor',
    keepAlive: true,
    permissions: ['behavior:monitor:view'],
    description: '实时行为监控页面，监控用户行为、系统状态、热门内容等'
  }
}

export const behaviorStatisticsRoute = {
  path: 'behavior/statistics',
  component: () => import('@/views/behavior/statistics/index.vue'),
  name: 'BehaviorStatistics',
  meta: {
    title: '行为统计',
    icon: 'data-analysis',
    keepAlive: true,
    permissions: ['behavior:statistics:view'],
    description: '行为统计分析页面，提供行为趋势、路径分析、热门内容统计等'
  }
}

// 导出所有行为分析路由
export const behaviorRoutes = [
  behaviorMonitorRoute,
  behaviorStatisticsRoute
]

export default behaviorRoutes
