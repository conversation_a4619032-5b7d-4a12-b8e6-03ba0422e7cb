<template>
  <div class="app-container">
    <!-- 统计概览 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon total">
              <el-icon><Operation /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ statsData.totalBehaviors || 0 }}</div>
              <div class="stats-label">总行为数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon today">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ statsData.todayBehaviors || 0 }}</div>
              <div class="stats-label">今日行为</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon avg">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ statsData.avgDuration || 0 }}s</div>
              <div class="stats-label">平均时长</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon rating">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ statsData.avgRating || 0 }}</div>
              <div class="stats-label">平均评分</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表分析 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 行为趋势分析 -->
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>行为趋势分析</span>
              <el-radio-group v-model="trendPeriod" @change="loadTrendData">
                <el-radio-button value="7">近7天</el-radio-button>
                <el-radio-button value="30">近30天</el-radio-button>
                <el-radio-button value="90">近90天</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="trendChartRef" style="height: 350px;"></div>
        </el-card>
      </el-col>
      
      <!-- 行为类型分布 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>行为类型分布</span>
          </template>
          <div ref="typeDistributionRef" style="height: 350px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户行为路径和热门内容 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 用户行为路径 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>用户行为路径分析</span>
              <el-button text @click="loadPathData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div ref="pathChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>
      
      <!-- 热门内容统计 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>热门内容统计</span>
          </template>
          <div class="popular-content" v-loading="contentLoading">
            <el-tabs v-model="activeTab" @tab-change="handleTabChange">
              <el-tab-pane label="热门知识库" name="knowledge">
                <div class="content-list">
                  <div 
                    v-for="(item, index) in popularContent.knowledge" 
                    :key="item.id"
                    class="content-item"
                  >
                    <div class="content-rank">{{ index + 1 }}</div>
                    <div class="content-info">
                      <div class="content-title">{{ item.title }}</div>
                      <div class="content-meta">
                        <span>{{ item.hitCount }}次访问</span>
                        <span>{{ item.category }}</span>
                      </div>
                    </div>
                    <div class="content-trend">
                      <el-icon :class="item.trend > 0 ? 'trend-up' : 'trend-down'">
                        <CaretTop v-if="item.trend > 0" />
                        <CaretBottom v-else />
                      </el-icon>
                      <span>{{ Math.abs(item.trend) }}%</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              
              <el-tab-pane label="热门搜索" name="search">
                <div class="content-list">
                  <div 
                    v-for="(item, index) in popularContent.search" 
                    :key="item.keyword"
                    class="content-item"
                  >
                    <div class="content-rank">{{ index + 1 }}</div>
                    <div class="content-info">
                      <div class="content-title">{{ item.keyword }}</div>
                      <div class="content-meta">
                        <span>{{ item.searchCount }}次搜索</span>
                        <span>{{ item.resultCount }}个结果</span>
                      </div>
                    </div>
                    <div class="content-trend">
                      <el-icon :class="item.trend > 0 ? 'trend-up' : 'trend-down'">
                        <CaretTop v-if="item.trend > 0" />
                        <CaretBottom v-else />
                      </el-icon>
                      <span>{{ Math.abs(item.trend) }}%</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 行为数据表格 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>行为数据详情</span>
          <div>
            <el-button type="warning" plain icon="Download" @click="handleExport">
              导出数据
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="用户ID" prop="userId">
          <el-input
            v-model="queryParams.userId"
            placeholder="请输入用户ID"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="行为类型" prop="behaviorType">
          <el-select v-model="queryParams.behaviorType" placeholder="行为类型" clearable style="width: 150px">
            <el-option label="搜索" value="search" />
            <el-option label="对话" value="chat" />
            <el-option label="查看" value="view" />
            <el-option label="反馈" value="feedback" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button 
            type="info" 
            icon="Sort" 
            @click="showSearch = !showSearch"
          >
            {{ showSearch ? '隐藏' : '显示' }}搜索
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 表格数据 -->
      <el-table :data="behaviorList" v-loading="tableLoading" stripe>
        <el-table-column label="行为ID" prop="behaviorId" width="100" />
        <el-table-column label="用户ID" prop="userId" width="120" />
        <el-table-column label="行为类型" prop="behaviorType" width="100">
          <template #default="scope">
            <el-tag :type="getBehaviorTypeTag(scope.row.behaviorType)" size="small">
              {{ getBehaviorTypeText(scope.row.behaviorType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="行为内容" prop="behaviorContent" show-overflow-tooltip />
        <el-table-column label="知识库ID" prop="knowledgeId" width="100" />
        <el-table-column label="会话ID" prop="sessionId" width="150" />
        <el-table-column label="持续时长" prop="duration" width="100">
          <template #default="scope">
            {{ scope.row.duration }}s
          </template>
        </el-table-column>
        <el-table-column label="评分" prop="rating" width="80">
          <template #default="scope">
            <el-rate 
              v-model="scope.row.rating" 
              disabled 
              size="small"
              :max="5"
            />
          </template>
        </el-table-column>
        <el-table-column label="行为时间" prop="behaviorTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.behaviorTime) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="BehaviorStatistics">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Operation, 
  Calendar, 
  Timer, 
  Star, 
  Refresh,
  CaretTop,
  CaretBottom
} from '@element-plus/icons-vue'
import {
  getBehaviorList,
  getBehaviorStatistics
} from '@/api/chatbot/behavior.js'
import { parseTime, addDateRange } from '@/utils/ruoyi'
import { download } from '@/utils/request'
import * as echarts from 'echarts'
import Pagination from '@/components/Pagination/index.vue'

// 响应式数据
const loading = ref(false)
const tableLoading = ref(false)
const contentLoading = ref(false)
const showSearch = ref(true)
const trendPeriod = ref('30')
const activeTab = ref('knowledge')

const statsData = reactive({
  totalBehaviors: 0,
  todayBehaviors: 0,
  avgDuration: 0,
  avgRating: 0
})

const behaviorList = ref([])
const popularContent = reactive({
  knowledge: [],
  search: []
})
const total = ref(0)
const dateRange = ref([])

// 图表引用
const trendChartRef = ref()
const typeDistributionRef = ref()
const pathChartRef = ref()

// 图表实例
let trendChart = null
let typeChart = null
let pathChart = null

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  userId: '',
  behaviorType: ''
})

// 初始化
onMounted(() => {
  loadStatsData()
  getList()
  loadPopularContent()
  nextTick(() => {
    initCharts()
  })
})

// 清理
onUnmounted(() => {
  if (trendChart) trendChart.dispose()
  if (typeChart) typeChart.dispose()
  if (pathChart) pathChart.dispose()
})

// 加载统计数据
const loadStatsData = async () => {
  try {
    const response = await getBehaviorStatistics({ type: 'overview' })
    Object.assign(statsData, response.data)
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取行为列表
const getList = async () => {
  try {
    tableLoading.value = true
    let params = { ...queryParams }
    params = addDateRange(params, dateRange.value, 'behaviorTime')
    
    const response = await getBehaviorList(params)
    behaviorList.value = response.rows || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取行为列表失败:', error)
    ElMessage.error('获取行为列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 加载热门内容
const loadPopularContent = async () => {
  try {
    contentLoading.value = true
    const response = await getBehaviorStatistics({ type: 'popular' })
    Object.assign(popularContent, response.data)
  } catch (error) {
    console.error('获取热门内容失败:', error)
  } finally {
    contentLoading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  initTrendChart()
  initTypeChart()
  initPathChart()
  
  window.addEventListener('resize', handleResize)
}

// 初始化趋势图表
const initTrendChart = async () => {
  if (!trendChartRef.value) return
  
  trendChart = echarts.init(trendChartRef.value)
  await loadTrendData()
}

// 加载趋势数据
const loadTrendData = async () => {
  try {
    const response = await getBehaviorStatistics({ type: 'trend', period: trendPeriod.value })
    const data = response.data || []
    
    const option = {
      title: {
        text: '行为趋势分析',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['搜索', '对话', '查看', '反馈'],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.date)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '搜索',
          type: 'line',
          data: data.map(item => item.search),
          smooth: true,
          itemStyle: { color: '#409eff' }
        },
        {
          name: '对话',
          type: 'line',
          data: data.map(item => item.chat),
          smooth: true,
          itemStyle: { color: '#67c23a' }
        },
        {
          name: '查看',
          type: 'line',
          data: data.map(item => item.view),
          smooth: true,
          itemStyle: { color: '#e6a23c' }
        },
        {
          name: '反馈',
          type: 'line',
          data: data.map(item => item.feedback),
          smooth: true,
          itemStyle: { color: '#f56c6c' }
        }
      ]
    }
    
    trendChart.setOption(option)
  } catch (error) {
    console.error('加载趋势数据失败:', error)
  }
}

// 初始化类型分布图表
const initTypeChart = async () => {
  if (!typeDistributionRef.value) return
  
  typeChart = echarts.init(typeDistributionRef.value)
  
  try {
    const response = await getBehaviorStatistics({ type: 'distribution' })
    const data = response.data || []
    
    const option = {
      title: {
        text: '行为类型分布',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [
        {
          name: '行为类型',
          type: 'pie',
          radius: ['40%', '70%'],
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    
    typeChart.setOption(option)
  } catch (error) {
    console.error('加载类型分布失败:', error)
  }
}

// 初始化路径图表
const initPathChart = async () => {
  if (!pathChartRef.value) return
  
  pathChart = echarts.init(pathChartRef.value)
  await loadPathData()
}

// 加载路径数据
const loadPathData = async () => {
  try {
    const response = await getBehaviorStatistics({ type: 'path' })
    const data = response.data || {}
    
    const option = {
      title: {
        text: '用户行为路径',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {},
      series: [
        {
          type: 'sankey',
          data: data.nodes || [],
          links: data.links || [],
          emphasis: {
            focus: 'adjacency'
          },
          lineStyle: {
            color: 'gradient',
            curveness: 0.5
          }
        }
      ]
    }
    
    pathChart.setOption(option)
  } catch (error) {
    console.error('加载路径数据失败:', error)
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  dateRange.value = []
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    userId: '',
    behaviorType: ''
  })
  getList()
}

// 标签页切换
const handleTabChange = (tab) => {
  // 可以根据需要加载不同的数据
}

// 导出数据
const handleExport = () => {
  // 使用通用的导出方法，实际项目中应该调用后端导出接口
  const exportData = behaviorList.value.map(item => ({
    行为ID: item.behaviorId,
    用户ID: item.userId,
    行为类型: getBehaviorTypeText(item.behaviorType),
    行为内容: item.behaviorContent,
    知识库ID: item.knowledgeId,
    会话ID: item.sessionId,
    持续时长: item.duration + 's',
    评分: item.rating,
    行为时间: parseTime(item.behaviorTime)
  }))

  // 这里可以使用第三方库如 xlsx 来导出 Excel
  console.log('导出数据:', exportData)
  ElMessage.success('导出功能需要后端支持')
}

// 处理窗口大小变化
const handleResize = () => {
  if (trendChart) trendChart.resize()
  if (typeChart) typeChart.resize()
  if (pathChart) pathChart.resize()
}

// 工具函数
const getBehaviorTypeTag = (type) => {
  const tags = {
    'search': 'primary',
    'chat': 'success',
    'view': 'warning',
    'feedback': 'danger'
  }
  return tags[type] || ''
}

const getBehaviorTypeText = (type) => {
  const texts = {
    'search': '搜索',
    'chat': '对话',
    'view': '查看',
    'feedback': '反馈'
  }
  return texts[type] || type
}
</script>

<style scoped>
.stats-card {
  height: 120px;
}

.stats-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.today {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.avg {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.rating {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #666;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 热门内容样式 */
.popular-content {
  height: 400px;
}

.content-list {
  max-height: 320px;
  overflow-y: auto;
}

.content-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.content-item:last-child {
  border-bottom: none;
}

.content-rank {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  margin-right: 12px;
}

.content-info {
  flex: 1;
  min-width: 0;
}

.content-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-meta {
  font-size: 12px;
  color: #999;
}

.content-meta span {
  margin-right: 12px;
}

.content-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.trend-up {
  color: #67c23a;
}

.trend-down {
  color: #f56c6c;
}
</style>
