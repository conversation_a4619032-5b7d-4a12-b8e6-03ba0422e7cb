/**
 * 行为分析模块权限配置
 * 定义行为分析相关的权限常量和权限检查函数
 */

// 行为分析权限常量
export const BEHAVIOR_PERMISSIONS = {
  // 行为监控权限
  BEHAVIOR_MONITOR_VIEW: 'behavior:monitor:view',
  BEHAVIOR_MONITOR_REALTIME: 'behavior:monitor:realtime',
  BEHAVIOR_MONITOR_EXPORT: 'behavior:monitor:export',
  
  // 行为统计权限
  BEHAVIOR_STATISTICS_VIEW: 'behavior:statistics:view',
  BEHAVIOR_STATISTICS_EXPORT: 'behavior:statistics:export',
  BEHAVIOR_STATISTICS_ANALYSIS: 'behavior:statistics:analysis',
  
  // 行为数据管理权限
  BEHAVIOR_DATA_VIEW: 'behavior:data:view',
  BEHAVIOR_DATA_DELETE: 'behavior:data:delete',
  BEHAVIOR_DATA_EXPORT: 'behavior:data:export',
  
  // 热门内容管理权限
  BEHAVIOR_POPULAR_VIEW: 'behavior:popular:view',
  BEHAVIOR_POPULAR_MANAGE: 'behavior:popular:manage'
}

// 权限组配置
export const BEHAVIOR_PERMISSION_GROUPS = {
  // 行为分析管理员权限组
  BEHAVIOR_ADMIN: [
    BEHAVIOR_PERMISSIONS.BEHAVIOR_MONITOR_VIEW,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_MONITOR_REALTIME,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_MONITOR_EXPORT,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_STATISTICS_VIEW,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_STATISTICS_EXPORT,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_STATISTICS_ANALYSIS,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_DATA_VIEW,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_DATA_DELETE,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_DATA_EXPORT,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_POPULAR_VIEW,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_POPULAR_MANAGE
  ],
  
  // 行为分析师权限组
  BEHAVIOR_ANALYST: [
    BEHAVIOR_PERMISSIONS.BEHAVIOR_MONITOR_VIEW,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_STATISTICS_VIEW,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_STATISTICS_EXPORT,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_STATISTICS_ANALYSIS,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_DATA_VIEW,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_POPULAR_VIEW
  ],
  
  // 行为查看员权限组
  BEHAVIOR_VIEWER: [
    BEHAVIOR_PERMISSIONS.BEHAVIOR_MONITOR_VIEW,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_STATISTICS_VIEW,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_DATA_VIEW,
    BEHAVIOR_PERMISSIONS.BEHAVIOR_POPULAR_VIEW
  ]
}

/**
 * 检查用户是否有指定权限
 * @param {string} permission 权限标识
 * @param {Array} userPermissions 用户权限列表
 * @returns {boolean} 是否有权限
 */
export function hasPermission(permission, userPermissions = []) {
  return userPermissions.includes(permission)
}

/**
 * 检查用户是否有权限组中的任一权限
 * @param {Array} permissions 权限列表
 * @param {Array} userPermissions 用户权限列表
 * @returns {boolean} 是否有权限
 */
export function hasAnyPermission(permissions, userPermissions = []) {
  return permissions.some(permission => userPermissions.includes(permission))
}

/**
 * 检查用户是否有权限组中的所有权限
 * @param {Array} permissions 权限列表
 * @param {Array} userPermissions 用户权限列表
 * @returns {boolean} 是否有权限
 */
export function hasAllPermissions(permissions, userPermissions = []) {
  return permissions.every(permission => userPermissions.includes(permission))
}

/**
 * 获取行为类型的显示文本
 * @param {string} behaviorType 行为类型
 * @returns {string} 显示文本
 */
export function getBehaviorTypeText(behaviorType) {
  const typeMap = {
    'search': '搜索',
    'chat': '对话',
    'view': '查看',
    'feedback': '反馈',
    'download': '下载',
    'share': '分享'
  }
  return typeMap[behaviorType] || behaviorType
}

/**
 * 获取行为类型的图标
 * @param {string} behaviorType 行为类型
 * @returns {string} 图标名称
 */
export function getBehaviorTypeIcon(behaviorType) {
  const iconMap = {
    'search': 'Search',
    'chat': 'ChatLineRound',
    'view': 'View',
    'feedback': 'Star',
    'download': 'Download',
    'share': 'Share'
  }
  return iconMap[behaviorType] || 'Operation'
}

export default {
  BEHAVIOR_PERMISSIONS,
  BEHAVIOR_PERMISSION_GROUPS,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  getBehaviorTypeText,
  getBehaviorTypeIcon
}
