-- 智能对话系统相关表结构

-- 知识图谱实体表
DROP TABLE IF EXISTS `bot_knowledge_entity`;
CREATE TABLE `bot_knowledge_entity` (
  `entity_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '实体ID',
  `entity_name` varchar(200) NOT NULL COMMENT '实体名称',
  `entity_type` varchar(50) NOT NULL COMMENT '实体类型(ATTRACTION/FOOD/HOTEL/TRANSPORT等)',
  `description` text COMMENT '实体描述',
  `properties` json COMMENT '实体属性(JSON格式)',
  `location` varchar(200) COMMENT '地理位置',
  `longitude` decimal(10,7) COMMENT '经度',
  `latitude` decimal(10,7) COMMENT '纬度',
  `tags` varchar(500) COMMENT '标签(逗号分隔)',
  `popularity` decimal(5,2) DEFAULT 0.00 COMMENT '热度评分',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`entity_id`),
  UNIQUE KEY `uk_entity_name_type` (`entity_name`, `entity_type`),
  KEY `idx_entity_type` (`entity_type`),
  KEY `idx_location` (`longitude`, `latitude`),
  KEY `idx_popularity` (`popularity`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识图谱实体表';

-- 知识图谱关系表
DROP TABLE IF EXISTS `bot_knowledge_relation`;
CREATE TABLE `bot_knowledge_relation` (
  `relation_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `source_entity_id` bigint(20) NOT NULL COMMENT '源实体ID',
  `target_entity_id` bigint(20) NOT NULL COMMENT '目标实体ID',
  `relation_type` varchar(50) NOT NULL COMMENT '关系类型(NEAR_BY/BELONGS_TO/SIMILAR_TO等)',
  `description` varchar(500) COMMENT '关系描述',
  `weight` decimal(5,2) DEFAULT 1.00 COMMENT '关系权重',
  `properties` json COMMENT '关系属性(JSON格式)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`relation_id`),
  KEY `idx_source_entity` (`source_entity_id`),
  KEY `idx_target_entity` (`target_entity_id`),
  KEY `idx_relation_type` (`relation_type`),
  KEY `idx_weight` (`weight`),
  CONSTRAINT `fk_relation_source` FOREIGN KEY (`source_entity_id`) REFERENCES `bot_knowledge_entity` (`entity_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_relation_target` FOREIGN KEY (`target_entity_id`) REFERENCES `bot_knowledge_entity` (`entity_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识图谱关系表';

-- 对话状态追踪表
DROP TABLE IF EXISTS `bot_dialogue_state`;
CREATE TABLE `bot_dialogue_state` (
  `state_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '状态ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `current_intent` varchar(50) COMMENT '当前意图',
  `slots` json COMMENT '槽位信息(JSON格式)',
  `context` json COMMENT '上下文信息(JSON格式)',
  `turn_count` int(11) DEFAULT 0 COMMENT '对话轮次',
  `status` char(1) DEFAULT '0' COMMENT '状态(0活跃 1完成 2中断)',
  `last_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`state_id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_intent` (`current_intent`),
  KEY `idx_status` (`status`),
  KEY `idx_update_time` (`last_update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对话状态追踪表';

-- 用户画像表
DROP TABLE IF EXISTS `bot_user_profile`;
CREATE TABLE `bot_user_profile` (
  `profile_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '画像ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `preferences` json COMMENT '偏好标签(JSON格式)',
  `interests` json COMMENT '兴趣领域(JSON格式)',
  `behaviors` json COMMENT '行为特征(JSON格式)',
  `user_group` varchar(50) COMMENT '人群分类',
  `activity_score` decimal(5,2) DEFAULT 0.00 COMMENT '活跃度评分',
  `satisfaction_score` decimal(5,2) DEFAULT 0.00 COMMENT '满意度评分',
  `consumption_level` varchar(20) COMMENT '消费能力等级',
  `travel_preference` varchar(100) COMMENT '旅游偏好类型',
  `last_analysis_time` datetime COMMENT '最后分析时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`profile_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_group` (`user_group`),
  KEY `idx_activity_score` (`activity_score`),
  KEY `idx_consumption_level` (`consumption_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户画像表';

-- 用户行为日志表
DROP TABLE IF EXISTS `bot_user_behavior_log`;
CREATE TABLE `bot_user_behavior_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `intent` varchar(50) COMMENT '意图',
  `entities` json COMMENT '实体信息(JSON格式)',
  `user_message` text COMMENT '用户消息',
  `ai_response` text COMMENT 'AI回复',
  `feedback` varchar(20) COMMENT '用户反馈(POSITIVE/NEGATIVE/NEUTRAL)',
  `response_time` int(11) COMMENT '响应时间(毫秒)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_intent` (`intent`),
  KEY `idx_feedback` (`feedback`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户行为日志表';

-- 推荐结果表
DROP TABLE IF EXISTS `bot_recommendation_result`;
CREATE TABLE `bot_recommendation_result` (
  `result_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `recommendation_type` varchar(50) NOT NULL COMMENT '推荐类型',
  `entity_id` bigint(20) COMMENT '推荐实体ID',
  `entity_name` varchar(200) COMMENT '推荐实体名称',
  `score` decimal(5,2) NOT NULL COMMENT '推荐评分',
  `reason` varchar(500) COMMENT '推荐理由',
  `details` json COMMENT '详细信息(JSON格式)',
  `is_clicked` tinyint(1) DEFAULT 0 COMMENT '是否被点击',
  `is_liked` tinyint(1) DEFAULT 0 COMMENT '是否被喜欢',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`result_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_recommendation_type` (`recommendation_type`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_score` (`score`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推荐结果表';

-- 意图识别配置表
DROP TABLE IF EXISTS `bot_intent_config`;
CREATE TABLE `bot_intent_config` (
  `config_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `intent_name` varchar(50) NOT NULL COMMENT '意图名称',
  `intent_description` varchar(200) COMMENT '意图描述',
  `keywords` json COMMENT '关键词列表(JSON格式)',
  `patterns` json COMMENT '匹配模式(JSON格式)',
  `required_slots` json COMMENT '必需槽位(JSON格式)',
  `response_templates` json COMMENT '回复模板(JSON格式)',
  `priority` int(11) DEFAULT 0 COMMENT '优先级',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`config_id`),
  UNIQUE KEY `uk_intent_name` (`intent_name`),
  KEY `idx_priority` (`priority`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='意图识别配置表';

-- 实体识别配置表
DROP TABLE IF EXISTS `bot_entity_config`;
CREATE TABLE `bot_entity_config` (
  `config_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `entity_type` varchar(50) NOT NULL COMMENT '实体类型',
  `entity_description` varchar(200) COMMENT '实体描述',
  `patterns` json COMMENT '识别模式(JSON格式)',
  `synonyms` json COMMENT '同义词(JSON格式)',
  `validation_rules` json COMMENT '验证规则(JSON格式)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`config_id`),
  UNIQUE KEY `uk_entity_type` (`entity_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实体识别配置表';

-- 插入基础配置数据
INSERT INTO `bot_intent_config` (`intent_name`, `intent_description`, `keywords`, `required_slots`, `priority`) VALUES
('ATTRACTION_QUERY', '景点查询', '["景点", "好玩", "哪里", "推荐", "银滩", "涠洲岛", "老街", "游玩"]', '["LOCATION"]', 10),
('FOOD_QUERY', '美食查询', '["美食", "吃", "海鲜", "小吃", "餐厅", "特色", "卷粉", "猪脚粉"]', '["LOCATION"]', 10),
('HOTEL_QUERY', '住宿查询', '["住宿", "酒店", "民宿", "住", "过夜", "客栈"]', '["LOCATION"]', 10),
('TRANSPORT_QUERY', '交通查询', '["交通", "怎么去", "路线", "车", "船", "机场", "火车"]', '["LOCATION"]', 10),
('WEATHER_QUERY', '天气查询', '["天气", "温度", "下雨", "晴天", "气候"]', '["LOCATION", "TIME"]', 8),
('ITINERARY_PLAN', '行程规划', '["行程", "规划", "几天", "安排", "计划", "路线"]', '["LOCATION", "DURATION"]', 9),
('PRICE_QUERY', '价格查询', '["价格", "多少钱", "费用", "门票", "花费", "预算"]', '["ITEM"]', 7),
('GREETING', '问候', '["你好", "您好", "hello", "hi", "早上好", "晚上好"]', '[]', 5);

INSERT INTO `bot_entity_config` (`entity_type`, `entity_description`, `patterns`) VALUES
('LOCATION', '地点实体', '["(北海|银滩|涠洲岛|老街|侨港|冠头岭|星岛湖)"]'),
('TIME', '时间实体', '["(\\\\d+天|\\\\d+小时|明天|后天|周末|今天|昨天)"]'),
('PRICE', '价格实体', '["(\\\\d+元|\\\\d+块|\\\\d+万)"]'),
('PERSON_COUNT', '人数实体', '["(\\\\d+人|一家人|两个人|三口之家)"]'),
('DURATION', '时长实体', '["(\\\\d+天|\\\\d+小时|\\\\d+分钟)"]'),
('ITEM', '物品实体', '["(门票|住宿|交通|餐饮)"]');

-- 数据采集任务表
DROP TABLE IF EXISTS `bot_data_collection_task`;
CREATE TABLE `bot_data_collection_task` (
  `task_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_name` varchar(200) NOT NULL COMMENT '任务名称',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型(SCHEDULED/MANUAL/TRIGGER)',
  `source_type` varchar(50) NOT NULL COMMENT '数据源类型(WEB_CRAWLER/API/DATABASE/FILE)',
  `source_config` json NOT NULL COMMENT '数据源配置(JSON格式)',
  `collection_rules` json NOT NULL COMMENT '采集规则(JSON格式)',
  `processing_rules` json COMMENT '数据处理规则(JSON格式)',
  `target_entity_type` varchar(50) NOT NULL COMMENT '目标实体类型',
  `cron_expression` varchar(100) COMMENT '执行频率(cron表达式)',
  `status` char(1) DEFAULT '0' COMMENT '任务状态(0待执行 1执行中 2已完成 3失败 4暂停)',
  `last_execute_time` datetime COMMENT '最后执行时间',
  `next_execute_time` datetime COMMENT '下次执行时间',
  `execute_count` int(11) DEFAULT 0 COMMENT '执行次数',
  `success_count` int(11) DEFAULT 0 COMMENT '成功次数',
  `failure_count` int(11) DEFAULT 0 COMMENT '失败次数',
  `last_result` varchar(500) COMMENT '最后执行结果',
  `error_message` text COMMENT '错误信息',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`task_id`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_source_type` (`source_type`),
  KEY `idx_target_entity_type` (`target_entity_type`),
  KEY `idx_status` (`status`),
  KEY `idx_next_execute_time` (`next_execute_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据采集任务表';

-- 数据采集结果表
DROP TABLE IF EXISTS `bot_data_collection_result`;
CREATE TABLE `bot_data_collection_result` (
  `result_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `batch_no` varchar(64) NOT NULL COMMENT '执行批次号',
  `source_url` varchar(500) COMMENT '数据源URL',
  `raw_data` longtext COMMENT '原始数据(JSON格式)',
  `processed_data` longtext COMMENT '处理后数据(JSON格式)',
  `entity_id` bigint(20) COMMENT '实体ID',
  `entity_name` varchar(200) COMMENT '实体名称',
  `entity_type` varchar(50) COMMENT '实体类型',
  `process_status` char(1) DEFAULT '0' COMMENT '处理状态(0待处理 1处理中 2处理成功 3处理失败 4已忽略)',
  `quality_score` decimal(5,2) COMMENT '数据质量评分',
  `confidence` decimal(5,2) COMMENT '置信度',
  `is_duplicate` char(1) DEFAULT '0' COMMENT '是否重复数据(0否 1是)',
  `duplicate_id` bigint(20) COMMENT '重复数据ID',
  `validation_result` varchar(100) COMMENT '验证结果',
  `error_message` text COMMENT '错误信息',
  `collect_time` datetime COMMENT '采集时间',
  `process_time` datetime COMMENT '处理时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`result_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_entity_type` (`entity_type`),
  KEY `idx_process_status` (`process_status`),
  KEY `idx_is_duplicate` (`is_duplicate`),
  KEY `idx_collect_time` (`collect_time`),
  CONSTRAINT `fk_collection_result_task` FOREIGN KEY (`task_id`) REFERENCES `bot_data_collection_task` (`task_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_collection_result_entity` FOREIGN KEY (`entity_id`) REFERENCES `bot_knowledge_entity` (`entity_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据采集结果表';
