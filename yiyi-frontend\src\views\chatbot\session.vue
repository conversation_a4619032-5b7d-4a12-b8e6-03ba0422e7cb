<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>会话管理</span>
          <div>
            <el-button type="primary" @click="refreshData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="用户名称" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入用户名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="会话状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择会话状态" clearable>
            <el-option label="进行中" value="1" />
            <el-option label="已结束" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格数据 -->
      <el-table v-loading="loading" :data="sessionList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="会话ID" align="center" prop="sessionId" width="200" />
        <el-table-column label="用户名称" align="center" prop="userName" />
        <el-table-column label="会话标题" align="center" prop="sessionTitle" show-overflow-tooltip />
        <el-table-column label="消息数量" align="center" prop="messageCount" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-tag :type="scope.row.status === '1' ? 'success' : 'info'">
              {{ scope.row.status === '1' ? '进行中' : '已结束' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="最后消息时间" align="center" prop="lastMessageTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.lastMessageTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              type="primary"
              text
              icon="View"
              @click="viewSession(scope.row)"
            >
              查看
            </el-button>
            <el-button
              v-if="scope.row.status === '1'"
              type="warning"
              text
              icon="Close"
              @click="endSession(scope.row)"
            >
              结束
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 会话详情对话框 -->
    <el-dialog title="会话详情" v-model="sessionDetailVisible" width="800px" append-to-body>
      <div v-if="currentSession">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="会话ID">{{ currentSession.sessionId }}</el-descriptions-item>
          <el-descriptions-item label="用户名称">{{ currentSession.userName }}</el-descriptions-item>
          <el-descriptions-item label="会话标题">{{ currentSession.sessionTitle }}</el-descriptions-item>
          <el-descriptions-item label="消息数量">{{ currentSession.messageCount }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentSession.status === '1' ? 'success' : 'info'">
              {{ currentSession.status === '1' ? '进行中' : '已结束' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ parseTime(currentSession.createTime) }}</el-descriptions-item>
        </el-descriptions>

        <!-- 消息历史 -->
        <div class="message-history" style="margin-top: 20px;">
          <h4>消息历史</h4>
          <div class="message-list" style="max-height: 400px; overflow-y: auto;">
            <ChatMessage
              v-for="message in sessionMessages"
              :key="message.messageId"
              :message="message"
              :show-header="true"
            />
            <el-empty v-if="sessionMessages.length === 0" description="暂无消息" />
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="sessionDetailVisible = false">关闭</el-button>
          <el-button 
            v-if="currentSession && currentSession.status === '1'"
            type="warning" 
            @click="endSessionFromDetail"
          >
            结束会话
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ChatbotSession">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { getActiveSessions, getSessionMessages, endSession as endSessionApi } from '@/api/chatbot/chatbot.js'
import { parseTime, addDateRange } from '@/utils/ruoyi'
import ChatMessage from '@/components/ChatMessage/index.vue'
import Pagination from '@/components/Pagination/index.vue'

// 响应式数据
const loading = ref(false)
const showSearch = ref(true)
const sessionList = ref([])
const sessionMessages = ref([])
const currentSession = ref(null)
const sessionDetailVisible = ref(false)
const total = ref(0)
const dateRange = ref([])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  userName: '',
  status: ''
})

// 初始化
onMounted(() => {
  getList()
})

// 获取会话列表
const getList = async () => {
  try {
    loading.value = true
    let params = { ...queryParams }
    params = addDateRange(params, dateRange.value)
    
    const response = await getActiveSessions(params)
    sessionList.value = response.data || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取会话列表失败:', error)
    ElMessage.error('获取会话列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  dateRange.value = []
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    userName: '',
    status: ''
  })
  getList()
}

// 刷新数据
const refreshData = () => {
  getList()
}

// 多选
const handleSelectionChange = (selection) => {
  // 处理多选逻辑
}

// 查看会话详情
const viewSession = async (row) => {
  try {
    currentSession.value = row
    sessionDetailVisible.value = true
    
    // 加载会话消息
    const response = await getSessionMessages(row.sessionId)
    sessionMessages.value = response.data || []
  } catch (error) {
    console.error('获取会话详情失败:', error)
    ElMessage.error('获取会话详情失败')
  }
}

// 结束会话
const endSession = (row) => {
  ElMessageBox.confirm(
    `确定要结束会话"${row.sessionTitle}"吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    endSessionAction(row.sessionId)
  })
}

// 从详情页结束会话
const endSessionFromDetail = () => {
  if (currentSession.value) {
    ElMessageBox.confirm(
      '确定要结束这个会话吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      endSessionAction(currentSession.value.sessionId)
    })
  }
}

// 执行结束会话
const endSessionAction = async (sessionId) => {
  try {
    await endSessionApi(sessionId)
    ElMessage.success('会话已结束')

    // 关闭详情对话框
    sessionDetailVisible.value = false

    // 刷新列表
    getList()
  } catch (error) {
    console.error('结束会话失败:', error)
    ElMessage.error('结束会话失败')
  }
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-list {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.message-list::-webkit-scrollbar {
  width: 6px;
}

.message-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
