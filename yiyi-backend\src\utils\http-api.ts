/**
 * HTTP API通信工具类
 * 替代WebSocket，使用HTTP API进行聊天通信
 */

// 保留消息接口定义，用于类型兼容
export interface ApiMessage {
    type: 'message' | 'heartbeat' | 'typing' | 'chatResponse' | 'error'
    content?: string
    messageType?: string
    senderName?: string
    sessionId?: string
    status?: string
    userMessage?: any
    aiMessage?: any
    relatedQuestions?: string[]
    timestamp?: number
    message?: string
}

// HTTP API配置接口
export interface HttpApiConfig {
    baseURL: string
    timeout?: number
    debug?: boolean
}

export type MessageEventHandler = (message: ApiMessage) => void

/**
 * HTTP API通信管理类
 * 提供与WebSocket相同的接口，但使用HTTP API实现
 */
export class H5HttpApiManager {
    private config: Required<HttpApiConfig>
    private isConnected = true // HTTP API模式下始终连接

    // 事件处理器
    private eventHandlers: Map<string, MessageEventHandler[]> = new Map()

    constructor(config: HttpApiConfig) {
        this.config = {
            timeout: 10000,
            debug: false,
            ...config
        }
    }

    /**
     * 连接（HTTP API模式下立即成功）
     */
    async connect(): Promise<void> {
        this.log('HTTP API模式连接成功')
        this.isConnected = true
        this.emit('open', { type: 'message', content: '连接成功' })
        return Promise.resolve()
    }

    /**
     * 断开连接
     */
    disconnect(): void {
        this.log('HTTP API模式断开连接')
        this.isConnected = false
        this.emit('close', { type: 'message', content: '连接已断开' })
    }

    /**
     * 发送消息（HTTP API模式下直接返回成功）
     */
    send(message: ApiMessage): boolean {
        if (!this.isConnected) {
            this.log('HTTP API未连接，无法发送消息')
            return false
        }

        this.log('HTTP API发送消息:', message)
        // HTTP API模式下，消息发送通过具体的API接口处理
        // 这里只是模拟WebSocket的接口，实际发送在store中处理
        return true
    }

    /**
     * 发送聊天消息
     */
    sendChatMessage(content: string, senderName?: string): boolean {
        return this.send({
            type: 'message',
            content,
            messageType: '1',
            senderName
        })
    }

    /**
     * 发送心跳（HTTP API模式下不需要）
     */
    sendHeartbeat(): boolean {
        this.log('HTTP API模式不需要心跳')
        return true
    }

    /**
     * 添加事件监听器
     */
    on(event: string, handler: MessageEventHandler): void {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, [])
        }
        this.eventHandlers.get(event)!.push(handler)
    }

    /**
     * 移除事件监听器
     */
    off(event: string, handler?: MessageEventHandler): void {
        if (!this.eventHandlers.has(event)) return

        const handlers = this.eventHandlers.get(event)!
        if (handler) {
            const index = handlers.indexOf(handler)
            if (index > -1) {
                handlers.splice(index, 1)
            }
        } else {
            handlers.length = 0
        }
    }

    /**
     * 触发事件
     */
    private emit(event: string, message: ApiMessage): void {
        const handlers = this.eventHandlers.get(event)
        if (handlers) {
            handlers.forEach(handler => {
                try {
                    handler(message)
                } catch (error) {
                    this.log('事件处理器执行失败:', error)
                }
            })
        }
    }

    /**
     * 获取连接状态
     */
    get connected(): boolean {
        return this.isConnected
    }

    /**
     * 日志输出
     */
    private log(...args: any[]): void {
        if (this.config.debug) {
            console.log('[H5HttpApi]', ...args)
        }
    }

    /**
     * 销毁实例
     */
    destroy(): void {
        this.disconnect()
        this.eventHandlers.clear()
    }
}

// 默认导出，保持与原WebSocket管理器的兼容性
export default H5HttpApiManager
