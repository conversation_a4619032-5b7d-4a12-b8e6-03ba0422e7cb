<template>
  <view class="lottery-page">
    <!-- 页面背景 -->
    <view class="page-background">
      <view class="bg-pattern"></view>
      <view class="floating-elements">
        <view class="floating-coin coin-1">💰</view>
        <view class="floating-coin coin-2">💰</view>
        <view class="floating-coin coin-3">💰</view>
        <view class="floating-star star-1">⭐</view>
        <view class="floating-star star-2">⭐</view>
        <view class="floating-star star-3">⭐</view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 页面标题 -->
      <view class="page-header">
        <view class="title-container">
          <text class="main-title">🎊 幸运大转盘 🎊</text>
          <text class="sub-title">转动转盘，赢取丰厚奖品</text>
        </view>
      </view>
      
      <!-- 用户信息卡片 -->
      <view class="user-card" v-if="userInfo">
        <view class="user-avatar">{{ userInfo.avatar || '👤' }}</view>
        <view class="user-details">
          <text class="user-name">{{ userInfo.nickname }}</text>
          <text class="user-phone">{{ userInfo.phone }}</text>
        </view>
        <view class="user-stats">
          <text class="stats-text">已抽奖 {{ drawCount }} 次</text>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-container" v-if="loading">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载抽奖数据...</text>
      </view>
      
      <!-- 错误提示 -->
      <view class="error-container" v-if="error">
        <text class="error-text">{{ error }}</text>
        <view class="retry-btn" @click="initLottery">
          <text class="retry-text">重试</text>
        </view>
      </view>
      
      <!-- 转盘组件 -->
      <LotteryWheel v-if="!loading && !error" />
      
      <!-- 奖品展示 -->
      <view class="prizes-section" v-if="prizes.length > 0">
        <view class="section-title">
          <text class="title-text">🎁 奖品一览 🎁</text>
        </view>
        <view class="prizes-grid">
          <view 
            v-for="prize in displayPrizes" 
            :key="prize.id"
            class="prize-item"
            :class="{ 'highlight': prize.level <= 3 }"
          >
            <view class="prize-icon">{{ prize.icon }}</view>
            <view class="prize-name">{{ prize.name }}</view>
            <view class="prize-level">{{ getPrizeLevelText(prize.level) }}</view>
            <view class="prize-value" v-if="prize.value && prize.value > 0">
              ¥{{ prize.value }}
            </view>
          </view>
        </view>
      </view>
      
      <!-- 活动说明 -->
      <view class="activity-info">
        <view class="info-title">
          <text class="title-text">📋 活动说明</text>
        </view>
        <view class="info-content">
          <view class="info-item">
            <text class="info-text">• 每位用户最多可抽奖 {{ maxDrawCount }} 次</text>
          </view>
          <view class="info-item">
            <text class="info-text">• 奖品数量有限，先到先得</text>
          </view>
          <view class="info-item">
            <text class="info-text">• 中奖结果以系统记录为准</text>
          </view>
          <view class="info-item">
            <text class="info-text">• 活动最终解释权归主办方所有</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 结果模态框 -->
    <LotteryResultModal 
      :visible="showResultModal"
      :result="currentResult"
      @close="closeResultModal"
      @continue="handleContinue"
    />
  </view>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useLotteryStore } from '@/store/lottery'
import { PrizeLevel } from '@/types/lottery'
import LotteryWheel from '@/components/LotteryWheel.vue'
import LotteryResultModal from '@/components/LotteryResultModal.vue'

// Store
const lotteryStore = useLotteryStore()

// 计算属性
const userInfo = computed(() => lotteryStore.user)
const prizes = computed(() => lotteryStore.prizes)
const loading = computed(() => lotteryStore.loading)
const error = computed(() => lotteryStore.error)
const drawCount = computed(() => lotteryStore.drawCount)
const currentResult = computed(() => lotteryStore.currentResult)
const showResultModal = computed(() => lotteryStore.showResultModal)
const maxDrawCount = computed(() => lotteryStore.lotteryConfig?.maxDrawCount || 3)

// 过滤掉安慰奖的奖品用于展示
const displayPrizes = computed(() => {
  return prizes.value.filter(prize => prize.level !== PrizeLevel.CONSOLATION)
})

// 方法
const initLottery = () => {
  lotteryStore.initLottery()
}

const closeResultModal = () => {
  lotteryStore.closeResultModal()
}

const handleContinue = () => {
  // 继续抽奖逻辑已在 store 中处理
}

const getPrizeLevelText = (level: PrizeLevel) => {
  const levelMap = {
    [PrizeLevel.FIRST]: '一等奖',
    [PrizeLevel.SECOND]: '二等奖',
    [PrizeLevel.THIRD]: '三等奖',
    [PrizeLevel.FOURTH]: '四等奖',
    [PrizeLevel.FIFTH]: '五等奖',
    [PrizeLevel.CONSOLATION]: '安慰奖',
    [PrizeLevel.NONE]: '未中奖'
  }
  return levelMap[level] || '未知'
}

// 生命周期
onMounted(() => {
  initLottery()
})
</script>

<style lang="scss" scoped>
.lottery-page {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.page-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: -2;
  
  .bg-pattern {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
      radial-gradient(circle at 80% 50%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
      radial-gradient(circle at 40% 20%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      radial-gradient(circle at 60% 80%, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 100px 100px, 100px 100px, 50px 50px, 50px 50px;
    animation: patternMove 20s linear infinite;
  }
  
  .floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    
    .floating-coin, .floating-star {
      position: absolute;
      font-size: 40rpx;
      animation: floatUpDown 6s infinite ease-in-out;
      opacity: 0.6;
    }
    
    .coin-1 {
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }
    
    .coin-2 {
      top: 60%;
      right: 15%;
      animation-delay: 2s;
    }
    
    .coin-3 {
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
    
    .star-1 {
      top: 30%;
      right: 10%;
      animation-delay: 1s;
    }
    
    .star-2 {
      top: 80%;
      left: 5%;
      animation-delay: 3s;
    }
    
    .star-3 {
      top: 50%;
      right: 5%;
      animation-delay: 5s;
    }
  }
}

.page-content {
  position: relative;
  z-index: 1;
  padding: 40rpx 30rpx;
}

.page-header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title-container {
    .main-title {
      display: block;
      font-size: 48rpx;
      font-weight: bold;
      color: white;
      text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
      margin-bottom: 10rpx;
    }
    
    .sub-title {
      display: block;
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.9);
      text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.3);
    }
  }
}

.user-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  display: flex;
  align-items: center;
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  
  .user-avatar {
    font-size: 60rpx;
    margin-right: 20rpx;
  }
  
  .user-details {
    flex: 1;
    
    .user-name {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: white;
      margin-bottom: 5rpx;
    }
    
    .user-phone {
      display: block;
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
  
  .user-stats {
    .stats-text {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.9);
      background: rgba(255, 255, 255, 0.1);
      padding: 10rpx 20rpx;
      border-radius: 20rpx;
    }
  }
}

.loading-container {
  text-align: center;
  padding: 100rpx 0;
  
  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid rgba(255, 255, 255, 0.3);
    border-top: 4rpx solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20rpx;
  }
  
  .loading-text {
    color: white;
    font-size: 28rpx;
  }
}

.error-container {
  text-align: center;
  padding: 100rpx 0;
  
  .error-text {
    color: #FF6B6B;
    font-size: 28rpx;
    margin-bottom: 30rpx;
    display: block;
  }
  
  .retry-btn {
    background: #FF6B6B;
    color: white;
    padding: 20rpx 40rpx;
    border-radius: 40rpx;
    display: inline-block;
    
    .retry-text {
      font-size: 28rpx;
      font-weight: bold;
    }
  }
}

.prizes-section {
  margin: 60rpx 0;
  
  .section-title {
    text-align: center;
    margin-bottom: 30rpx;
    
    .title-text {
      font-size: 36rpx;
      font-weight: bold;
      color: white;
      text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
    }
  }
  
  .prizes-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
    
    .prize-item {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 20rpx;
      padding: 30rpx 20rpx;
      text-align: center;
      backdrop-filter: blur(10px);
      border: 1rpx solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
      
      &.highlight {
        background: rgba(255, 215, 0, 0.2);
        border-color: rgba(255, 215, 0, 0.5);
        box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.3);
      }
      
      .prize-icon {
        font-size: 50rpx;
        margin-bottom: 10rpx;
      }
      
      .prize-name {
        font-size: 26rpx;
        font-weight: bold;
        color: white;
        margin-bottom: 5rpx;
        display: block;
      }
      
      .prize-level {
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 5rpx;
        display: block;
      }
      
      .prize-value {
        font-size: 24rpx;
        font-weight: bold;
        color: #FFD700;
        display: block;
      }
    }
  }
}

.activity-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  margin-top: 40rpx;
  
  .info-title {
    text-align: center;
    margin-bottom: 20rpx;
    
    .title-text {
      font-size: 32rpx;
      font-weight: bold;
      color: white;
      text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.3);
    }
  }
  
  .info-content {
    .info-item {
      margin-bottom: 15rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-text {
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.5;
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes patternMove {
  0% {
    transform: translateX(0) translateY(0);
  }
  100% {
    transform: translateX(-100px) translateY(-100px);
  }
}

@keyframes floatUpDown {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-30rpx) rotate(180deg);
  }
}
</style>
