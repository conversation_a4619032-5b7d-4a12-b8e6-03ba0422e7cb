import { ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth'

class ChatWebSocket {
  constructor() {
    this.websocket = null
    this.reconnectTimer = null
    this.reconnectCount = 0
    this.maxReconnectCount = 5
    this.reconnectInterval = 3000
    this.isConnected = false
    this.messageHandlers = []
    this.errorHandlers = []
    this.closeHandlers = []
    this.openHandlers = []
  }

  // 连接WebSocket
  connect(userId) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      try {
        const token = getToken()
        if (!token) {
          reject(new Error('未获取到用户token'))
          return
        }

        // 构建WebSocket URL
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
        const host = window.location.host
        const wsUrl = `${protocol}//${host}/websocket/chat/${userId}`
        
        this.websocket = new WebSocket(wsUrl)

        this.websocket.onopen = (event) => {
          console.log('WebSocket连接成功')
          this.isConnected = true
          this.reconnectCount = 0
          this.openHandlers.forEach(handler => handler(event))
          resolve()
        }

        this.websocket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.messageHandlers.forEach(handler => handler(data))
          } catch (error) {
            console.error('解析WebSocket消息失败:', error)
          }
        }

        this.websocket.onclose = (event) => {
          console.log('WebSocket连接关闭')
          this.isConnected = false
          this.closeHandlers.forEach(handler => handler(event))
          
          // 如果不是主动关闭，则尝试重连
          if (event.code !== 1000 && this.reconnectCount < this.maxReconnectCount) {
            this.reconnect(userId)
          }
        }

        this.websocket.onerror = (error) => {
          console.error('WebSocket连接错误:', error)
          this.isConnected = false
          this.errorHandlers.forEach(handler => handler(error))
          reject(error)
        }

      } catch (error) {
        reject(error)
      }
    })
  }

  // 重连
  reconnect(userId) {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    this.reconnectTimer = setTimeout(() => {
      this.reconnectCount++
      console.log(`尝试重连WebSocket (${this.reconnectCount}/${this.maxReconnectCount})`)
      
      this.connect(userId).catch(() => {
        if (this.reconnectCount >= this.maxReconnectCount) {
          ElMessage.error('WebSocket连接失败，请刷新页面重试')
        }
      })
    }, this.reconnectInterval)
  }

  // 发送消息
  sendMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message))
      return true
    } else {
      ElMessage.error('连接已断开，请稍后重试')
      return false
    }
  }

  // 关闭连接
  close() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    if (this.websocket) {
      this.websocket.close(1000, '主动关闭')
      this.websocket = null
    }
    
    this.isConnected = false
  }

  // 添加消息处理器
  onMessage(handler) {
    this.messageHandlers.push(handler)
  }

  // 添加错误处理器
  onError(handler) {
    this.errorHandlers.push(handler)
  }

  // 添加关闭处理器
  onClose(handler) {
    this.closeHandlers.push(handler)
  }

  // 添加打开处理器
  onOpen(handler) {
    this.openHandlers.push(handler)
  }

  // 移除处理器
  removeHandler(type, handler) {
    const handlers = {
      message: this.messageHandlers,
      error: this.errorHandlers,
      close: this.closeHandlers,
      open: this.openHandlers
    }
    
    const targetHandlers = handlers[type]
    if (targetHandlers) {
      const index = targetHandlers.indexOf(handler)
      if (index > -1) {
        targetHandlers.splice(index, 1)
      }
    }
  }

  // 获取连接状态
  getReadyState() {
    return this.websocket ? this.websocket.readyState : WebSocket.CLOSED
  }

  // 检查是否连接
  isConnectedState() {
    return this.isConnected && this.websocket && this.websocket.readyState === WebSocket.OPEN
  }
}

// 创建单例
const chatWebSocket = new ChatWebSocket()

export default chatWebSocket
