<template>
  <wd-watermark content="小程序平台" :opacity="0.25"></wd-watermark>
  <view class="content">
    <image src="/static/logo-icon.png" class="logo" />
    <text>{{ title }}</text>
    <wd-button type="primary" @click="toUIComponentsDoc">wot-design-uni 文档</wd-button>
    <text>pinia count: {{ countInfo.count }}</text>
    <wd-button type="primary" @click="onHandleClick">add count</wd-button>
    <wd-button type="success" @click="toLotteryPage">🎊 幸运大转盘 🎊</wd-button>
  </view>
</template>

<script setup lang="ts">
import { useCountStore } from "@/store";

const title = ref("小程序平台");
const { countInfo, addCount } = useCountStore();

function toUIComponentsDoc() {
  window.open("https://wot-design-uni.netlify.app/component/button.html");
}

function onHandleClick() {
  addCount();
}

function toLotteryPage() {
  uni.navigateTo({
    url: "/pages/lottery/index",
  });
}
</script>

<style lang="scss">
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx {
    top: 200rpx;
  }
  gap: 24rpx;

  .logo {
    width: 80rpx;
    height: 80rpx;
  }
}
</style>
