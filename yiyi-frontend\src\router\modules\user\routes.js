/**
 * 用户管理路由详细配置
 * 定义用户管理模块的所有路由规则和权限
 */

export const userListRoute = {
  path: 'user/list',
  component: () => import('@/views/user/list/index.vue'),
  name: 'UserList',
  meta: { 
    title: '用户列表', 
    icon: 'user',
    keepAlive: true,
    permissions: ['user:list:view'],
    description: '用户列表管理页面，支持用户搜索、筛选、状态管理等功能'
  }
}

export const userDetailRoute = {
  path: 'user/detail/:id',
  component: () => import('@/views/user/detail/index.vue'),
  name: 'UserDetail',
  meta: { 
    title: '用户详情', 
    hidden: true,
    activeMenu: '/chatbot/user/list',
    permissions: ['user:detail:view'],
    description: '用户详情页面，显示用户完整信息、行为记录、标签管理等'
  }
}

export const userStatisticsRoute = {
  path: 'user/statistics',
  component: () => import('@/views/user/statistics/index.vue'),
  name: 'UserStatistics',
  meta: { 
    title: '用户统计', 
    icon: 'data-analysis',
    keepAlive: true,
    permissions: ['user:statistics:view'],
    description: '用户统计分析页面，提供用户增长、分布、活跃度等数据分析'
  }
}

// 导出所有用户管理路由
export const userRoutes = [
  userListRoute,
  userDetailRoute,
  userStatisticsRoute
]

export default userRoutes
