<template>
  <div class="app-container">
    <!-- 实时监控概览 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="monitor-card">
          <div class="monitor-item">
            <div class="monitor-icon online">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="monitor-content">
              <div class="monitor-number">{{ monitorData.onlineUsers || 0 }}</div>
              <div class="monitor-label">在线用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="monitor-card">
          <div class="monitor-item">
            <div class="monitor-icon active">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="monitor-content">
              <div class="monitor-number">{{ monitorData.activeSessions || 0 }}</div>
              <div class="monitor-label">活跃会话</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="monitor-card">
          <div class="monitor-item">
            <div class="monitor-icon behavior">
              <el-icon><Operation /></el-icon>
            </div>
            <div class="monitor-content">
              <div class="monitor-number">{{ monitorData.todayBehaviors || 0 }}</div>
              <div class="monitor-label">今日行为</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="monitor-card">
          <div class="monitor-item">
            <div class="monitor-icon knowledge">
              <el-icon><Collection /></el-icon>
            </div>
            <div class="monitor-content">
              <div class="monitor-number">{{ monitorData.knowledgeHits || 0 }}</div>
              <div class="monitor-label">知识库命中</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时图表 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 实时行为监控 -->
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>实时行为监控</span>
              <div>
                <el-switch
                  v-model="autoRefresh"
                  active-text="自动刷新"
                  @change="toggleAutoRefresh"
                />
                <el-button type="primary" size="small" @click="refreshData" style="margin-left: 10px;">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          <div ref="behaviorChartRef" style="height: 350px;"></div>
        </el-card>
      </el-col>
      
      <!-- 行为类型统计 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>行为类型统计</span>
          </template>
          <div ref="typeChartRef" style="height: 350px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 热门知识库和实时行为流 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 热门知识库排行 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>热门知识库排行</span>
              <el-button text @click="loadPopularKnowledge">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="popular-list" v-loading="popularLoading">
            <div 
              v-for="(item, index) in popularKnowledge" 
              :key="item.knowledgeId"
              class="popular-item"
            >
              <div class="popular-rank">
                <span class="rank-number" :class="getRankClass(index)">{{ index + 1 }}</span>
              </div>
              <div class="popular-content">
                <div class="popular-title">{{ item.title }}</div>
                <div class="popular-meta">
                  <el-tag size="small" type="info">{{ item.categoryName }}</el-tag>
                  <span class="popular-time">{{ item.updateTime }}</span>
                </div>
              </div>
              <div class="popular-count">
                <div class="count-number">{{ item.hitCount }}</div>
                <div class="count-label">次</div>
              </div>
            </div>
            <el-empty v-if="popularKnowledge.length === 0" description="暂无数据" :image-size="60" />
          </div>
        </el-card>
      </el-col>
      
      <!-- 实时行为流 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>实时行为流</span>
              <el-tag :type="realtimeStatus ? 'success' : 'danger'" size="small">
                {{ realtimeStatus ? '已连接' : '未连接' }}
              </el-tag>
            </div>
          </template>
          
          <div class="behavior-stream" ref="behaviorStreamRef">
            <div 
              v-for="behavior in realtimeBehaviors" 
              :key="behavior.id"
              class="behavior-stream-item"
              :class="{ 'new-item': behavior.isNew }"
            >
              <div class="behavior-avatar">
                <el-avatar :size="32" :src="behavior.userAvatar">
                  {{ behavior.userName?.charAt(0) }}
                </el-avatar>
              </div>
              <div class="behavior-info">
                <div class="behavior-user">{{ behavior.userName }}</div>
                <div class="behavior-action">{{ getBehaviorText(behavior.type) }}</div>
                <div class="behavior-time">{{ formatTime(behavior.time) }}</div>
              </div>
              <div class="behavior-status">
                <el-icon :class="getBehaviorIcon(behavior.type)"></el-icon>
              </div>
            </div>
            <el-empty v-if="realtimeBehaviors.length === 0" description="暂无实时数据" :image-size="60" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户活跃度热力图 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>用户活跃度热力图</span>
          <el-radio-group v-model="heatmapPeriod" @change="loadHeatmapData">
            <el-radio-button value="today">今日</el-radio-button>
            <el-radio-button value="week">本周</el-radio-button>
            <el-radio-button value="month">本月</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <div ref="heatmapChartRef" style="height: 300px;"></div>
    </el-card>
  </div>
</template>

<script setup name="BehaviorMonitor">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Connection, 
  ChatDotRound, 
  Operation, 
  Collection, 
  Refresh,
  Search,
  View,
  ChatLineRound,
  Star
} from '@element-plus/icons-vue'
import {
  getBehaviorStatistics,
  getPopularKnowledge,
  getUserActivity
} from '@/api/chatbot/behavior.js'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const popularLoading = ref(false)
const autoRefresh = ref(true)
const realtimeStatus = ref(false)
const heatmapPeriod = ref('today')

const monitorData = reactive({
  onlineUsers: 0,
  activeSessions: 0,
  todayBehaviors: 0,
  knowledgeHits: 0
})

const popularKnowledge = ref([])
const realtimeBehaviors = ref([])

// 图表引用
const behaviorChartRef = ref()
const typeChartRef = ref()
const heatmapChartRef = ref()
const behaviorStreamRef = ref()

// 图表实例
let behaviorChart = null
let typeChart = null
let heatmapChart = null

// 定时器
let refreshTimer = null
let realtimeTimer = null

// 初始化
onMounted(() => {
  loadMonitorData()
  loadPopularKnowledge()
  nextTick(() => {
    initCharts()
    startRealtime()
  })
})

// 清理
onUnmounted(() => {
  if (behaviorChart) behaviorChart.dispose()
  if (typeChart) typeChart.dispose()
  if (heatmapChart) heatmapChart.dispose()
  if (refreshTimer) clearInterval(refreshTimer)
  if (realtimeTimer) clearInterval(realtimeTimer)
})

// 加载监控数据
const loadMonitorData = async () => {
  try {
    const response = await getBehaviorStatistics({ type: 'overview' })
    Object.assign(monitorData, response.data)
  } catch (error) {
    console.error('获取监控数据失败:', error)
  }
}

// 加载热门知识库
const loadPopularKnowledge = async () => {
  try {
    popularLoading.value = true
    const response = await getPopularKnowledge({ limit: 10 })
    popularKnowledge.value = response.data || []
  } catch (error) {
    console.error('获取热门知识库失败:', error)
  } finally {
    popularLoading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  initBehaviorChart()
  initTypeChart()
  initHeatmapChart()
  
  window.addEventListener('resize', handleResize)
}

// 初始化行为监控图表
const initBehaviorChart = async () => {
  if (!behaviorChartRef.value) return
  
  behaviorChart = echarts.init(behaviorChartRef.value)
  await loadBehaviorData()
}

// 加载行为数据
const loadBehaviorData = async () => {
  try {
    const response = await getBehaviorStatistics({ type: 'trend', period: 24 })
    const data = response.data || []
    
    const option = {
      title: {
        text: '实时行为趋势',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['搜索', '对话', '查看', '反馈'],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.time)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '搜索',
          type: 'line',
          data: data.map(item => item.search),
          smooth: true,
          itemStyle: { color: '#409eff' }
        },
        {
          name: '对话',
          type: 'line',
          data: data.map(item => item.chat),
          smooth: true,
          itemStyle: { color: '#67c23a' }
        },
        {
          name: '查看',
          type: 'line',
          data: data.map(item => item.view),
          smooth: true,
          itemStyle: { color: '#e6a23c' }
        },
        {
          name: '反馈',
          type: 'line',
          data: data.map(item => item.feedback),
          smooth: true,
          itemStyle: { color: '#f56c6c' }
        }
      ]
    }
    
    behaviorChart.setOption(option)
  } catch (error) {
    console.error('加载行为数据失败:', error)
  }
}

// 初始化类型统计图表
const initTypeChart = async () => {
  if (!typeChartRef.value) return
  
  typeChart = echarts.init(typeChartRef.value)
  
  try {
    const response = await getBehaviorStatistics({ type: 'distribution' })
    const data = response.data || []
    
    const option = {
      title: {
        text: '行为类型分布',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [
        {
          name: '行为类型',
          type: 'pie',
          radius: ['40%', '70%'],
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    
    typeChart.setOption(option)
  } catch (error) {
    console.error('加载类型统计失败:', error)
  }
}

// 初始化热力图
const initHeatmapChart = async () => {
  if (!heatmapChartRef.value) return
  
  heatmapChart = echarts.init(heatmapChartRef.value)
  await loadHeatmapData()
}

// 加载热力图数据
const loadHeatmapData = async () => {
  try {
    const response = await getUserActivity({ type: 'heatmap', period: heatmapPeriod.value })
    const data = response.data || []
    
    const option = {
      title: {
        text: '用户活跃度热力图',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        position: 'top'
      },
      grid: {
        height: '50%',
        top: '10%'
      },
      xAxis: {
        type: 'category',
        data: Array.from({length: 24}, (_, i) => i + ':00'),
        splitArea: {
          show: true
        }
      },
      yAxis: {
        type: 'category',
        data: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
        splitArea: {
          show: true
        }
      },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '15%'
      },
      series: [{
        name: '活跃度',
        type: 'heatmap',
        data: data,
        label: {
          show: true
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    
    heatmapChart.setOption(option)
  } catch (error) {
    console.error('加载热力图数据失败:', error)
  }
}

// 开始实时监控
const startRealtime = () => {
  realtimeStatus.value = true
  loadRealtimeBehaviors()
  
  realtimeTimer = setInterval(() => {
    loadRealtimeBehaviors()
  }, 3000) // 每3秒更新一次
}

// 加载实时行为
const loadRealtimeBehaviors = async () => {
  try {
    const response = await getBehaviorStatistics({ type: 'realtime', limit: 20 })
    const newBehaviors = response.data || []
    
    // 标记新行为
    newBehaviors.forEach(behavior => {
      behavior.isNew = true
      behavior.id = Date.now() + Math.random()
    })
    
    // 更新行为流
    realtimeBehaviors.value = [...newBehaviors, ...realtimeBehaviors.value].slice(0, 20)
    
    // 移除新标记
    setTimeout(() => {
      realtimeBehaviors.value.forEach(behavior => {
        behavior.isNew = false
      })
    }, 1000)
    
  } catch (error) {
    console.error('获取实时行为失败:', error)
    realtimeStatus.value = false
  }
}

// 切换自动刷新
const toggleAutoRefresh = (value) => {
  if (value) {
    refreshTimer = setInterval(() => {
      loadMonitorData()
      loadBehaviorData()
    }, 30000) // 每30秒刷新一次
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

// 手动刷新
const refreshData = () => {
  loadMonitorData()
  loadBehaviorData()
  loadPopularKnowledge()
  ElMessage.success('数据已刷新')
}

// 处理窗口大小变化
const handleResize = () => {
  if (behaviorChart) behaviorChart.resize()
  if (typeChart) typeChart.resize()
  if (heatmapChart) heatmapChart.resize()
}

// 工具函数
const getRankClass = (index) => {
  if (index === 0) return 'rank-first'
  if (index === 1) return 'rank-second'
  if (index === 2) return 'rank-third'
  return 'rank-normal'
}

const getBehaviorText = (type) => {
  const texts = {
    'search': '搜索知识库',
    'chat': '发起对话',
    'view': '查看详情',
    'feedback': '反馈评价'
  }
  return texts[type] || type
}

const getBehaviorIcon = (type) => {
  const icons = {
    'search': 'Search',
    'chat': 'ChatLineRound',
    'view': 'View',
    'feedback': 'Star'
  }
  return icons[type] || 'Operation'
}

const formatTime = (time) => {
  const now = new Date()
  const target = new Date(time)
  const diff = now - target

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'
  if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'
  return Math.floor(diff / 86400000) + '天前'
}
</script>

<style scoped>
.monitor-card {
  height: 100px;
}

.monitor-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.monitor-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.monitor-icon.online {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.monitor-icon.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.monitor-icon.behavior {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.monitor-icon.knowledge {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.monitor-content {
  flex: 1;
}

.monitor-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.monitor-label {
  font-size: 12px;
  color: #666;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 热门知识库样式 */
.popular-list {
  max-height: 350px;
  overflow-y: auto;
}

.popular-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.popular-item:last-child {
  border-bottom: none;
}

.popular-rank {
  width: 40px;
  margin-right: 12px;
}

.rank-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.rank-first {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
}

.rank-second {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #333;
}

.rank-third {
  background: linear-gradient(135deg, #cd7f32, #daa520);
}

.rank-normal {
  background-color: #909399;
}

.popular-content {
  flex: 1;
  min-width: 0;
}

.popular-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.popular-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.popular-time {
  font-size: 12px;
  color: #999;
}

.popular-count {
  text-align: center;
  margin-left: 12px;
}

.count-number {
  font-size: 16px;
  font-weight: bold;
  color: #f56c6c;
  line-height: 1;
}

.count-label {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

/* 实时行为流样式 */
.behavior-stream {
  max-height: 350px;
  overflow-y: auto;
}

.behavior-stream-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.behavior-stream-item.new-item {
  background-color: #f0f8ff;
  margin: 0 -12px;
  padding: 8px 12px;
  border-radius: 6px;
}

.behavior-stream-item:last-child {
  border-bottom: none;
}

.behavior-avatar {
  margin-right: 12px;
}

.behavior-info {
  flex: 1;
  min-width: 0;
}

.behavior-user {
  font-weight: 500;
  color: #333;
  font-size: 14px;
  margin-bottom: 2px;
}

.behavior-action {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.behavior-time {
  font-size: 11px;
  color: #999;
}

.behavior-status {
  color: #409eff;
  font-size: 16px;
}
</style>
