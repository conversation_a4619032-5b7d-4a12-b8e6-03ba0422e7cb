-- 用户管理相关表结构

-- 聊天用户表
DROP TABLE IF EXISTS `chat_user`;
CREATE TABLE `chat_user`
(
    `user_id`           varchar(50) NOT NULL COMMENT '用户ID',
    `nickname`          varchar(50) NOT NULL COMMENT '用户昵称',
    `avatar`            varchar(500) DEFAULT NULL COMMENT '用户头像',
    `phone_number`      varchar(20) NOT NULL COMMENT '手机号码',
    `email`             varchar(100) DEFAULT NULL COMMENT '邮箱',
    `gender`            char(1)      DEFAULT '2' COMMENT '性别（0男 1女 2未知）',
    `age_group`         tinyint      DEFAULT NULL COMMENT '年龄段（1=18-25, 2=26-35, 3=36-45, 4=46-55, 5=55+）',
    `travel_preference` varchar(100) DEFAULT NULL COMMENT '旅行偏好（亲子/情侣/商务/休闲/探险）',
    `city`              varchar(50)  DEFAULT NULL COMMENT '常住城市',
    `status`            char(1)      DEFAULT '0' COMMENT '用户状态（0正常 1停用）',
    `login_ip`          varchar(128) DEFAULT NULL COMMENT '最后登录IP',
    `login_date`        datetime     DEFAULT NULL COMMENT '最后登录时间',
    `register_time`     datetime     DEFAULT NULL COMMENT '注册时间',
    `total_chats`       bigint       DEFAULT '0' COMMENT '总对话次数',
    `user_level`        tinyint      DEFAULT '1' COMMENT '用户等级（1普通 2VIP 3SVIP）',
    `user_tags`         text COMMENT '用户标签（JSON格式存储）',
    `create_by`         varchar(64)  DEFAULT '' COMMENT '创建者',
    `create_time`       datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`         varchar(64)  DEFAULT '' COMMENT '更新者',
    `update_time`       datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`            varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`user_id`),
    UNIQUE KEY `uk_phone_number` (`phone_number`),
    KEY `idx_nickname` (`nickname`),
    KEY `idx_city` (`city`),
    KEY `idx_status` (`status`),
    KEY `idx_register_time` (`register_time`),
    KEY `idx_login_date` (`login_date`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='聊天用户表';

-- 用户行为记录表
DROP TABLE IF EXISTS `user_behavior`;
CREATE TABLE `user_behavior`
(
    `behavior_id`      bigint      NOT NULL AUTO_INCREMENT COMMENT '行为ID',
    `user_id`          varchar(50) NOT NULL COMMENT '用户ID',
    `behavior_type`    varchar(20) NOT NULL COMMENT '行为类型（search/click/chat/feedback/view）',
    `behavior_content` text COMMENT '行为内容（JSON格式）',
    `knowledge_id`     bigint       DEFAULT NULL COMMENT '关联知识库ID',
    `session_id`       varchar(100) DEFAULT NULL COMMENT '会话ID',
    `user_ip`          varchar(128) DEFAULT NULL COMMENT '用户IP',
    `user_agent`       varchar(500) DEFAULT NULL COMMENT '用户代理',
    `behavior_time`    datetime    NOT NULL COMMENT '行为时间',
    `duration`         int          DEFAULT NULL COMMENT '持续时长（秒）',
    `rating`           tinyint      DEFAULT NULL COMMENT '评分（1-5分）',
    `create_by`        varchar(64)  DEFAULT '' COMMENT '创建者',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`        varchar(64)  DEFAULT '' COMMENT '更新者',
    `update_time`      datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`           varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`behavior_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_behavior_type` (`behavior_type`),
    KEY `idx_knowledge_id` (`knowledge_id`),
    KEY `idx_session_id` (`session_id`),
    KEY `idx_behavior_time` (`behavior_time`),
    KEY `idx_user_behavior_time` (`user_id`, `behavior_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='用户行为记录表';

-- 用户反馈表
DROP TABLE IF EXISTS `user_feedback`;
CREATE TABLE `user_feedback`
(
    `feedback_id`      bigint      NOT NULL AUTO_INCREMENT COMMENT '反馈ID',
    `user_id`          varchar(50) NOT NULL COMMENT '用户ID',
    `session_id`       varchar(100) DEFAULT NULL COMMENT '会话ID',
    `message_id`       bigint       DEFAULT NULL COMMENT '消息ID',
    `feedback_type`    varchar(20) NOT NULL COMMENT '反馈类型（like/dislike/report/suggestion）',
    `feedback_content` text COMMENT '反馈内容',
    `rating`           tinyint      DEFAULT NULL COMMENT '评分（1-5分）',
    `tags`             varchar(200) DEFAULT NULL COMMENT '标签',
    `status`           char(1)      DEFAULT '0' COMMENT '处理状态（0待处理 1已处理 2已忽略）',
    `handler`          varchar(64)  DEFAULT NULL COMMENT '处理人',
    `handle_time`      datetime     DEFAULT NULL COMMENT '处理时间',
    `handle_result`    text COMMENT '处理结果',
    `create_by`        varchar(64)  DEFAULT '' COMMENT '创建者',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`        varchar(64)  DEFAULT '' COMMENT '更新者',
    `update_time`      datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`           varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`feedback_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_session_id` (`session_id`),
    KEY `idx_message_id` (`message_id`),
    KEY `idx_feedback_type` (`feedback_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='用户反馈表';

-- 用户偏好表
DROP TABLE IF EXISTS `user_preference`;
CREATE TABLE `user_preference`
(
    `preference_id`        bigint      NOT NULL AUTO_INCREMENT COMMENT '偏好ID',
    `user_id`              varchar(50) NOT NULL COMMENT '用户ID',
    `category_preferences` text COMMENT '分类偏好（JSON格式）',
    `keyword_preferences`  text COMMENT '关键词偏好（JSON格式）',
    `search_history`       text COMMENT '搜索历史（JSON格式）',
    `favorite_knowledge`   text COMMENT '收藏知识库（JSON格式）',
    `interaction_patterns` text COMMENT '交互模式（JSON格式）',
    `last_update_time`     datetime     DEFAULT NULL COMMENT '最后更新时间',
    `create_by`            varchar(64)  DEFAULT '' COMMENT '创建者',
    `create_time`          datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`            varchar(64)  DEFAULT '' COMMENT '更新者',
    `update_time`          datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`               varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`preference_id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    KEY `idx_last_update_time` (`last_update_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='用户偏好表';

-- 插入测试数据
INSERT INTO `chat_user`
VALUES ('U1736604000001', '北海游客001', 'https://example.com/avatar1.jpg', '13800138001', '<EMAIL>', '1',
        2, '亲子', '广州', '0', '192.168.1.100', '2025-01-11 10:00:00', '2025-01-01 09:00:00', 15, 1,
        '{"interests":["海滩","美食"],"source":"微信"}', 'system', '2025-01-01 09:00:00', '', NULL, '测试用户1'),
       ('U1736604000002', '北海游客002', 'https://example.com/avatar2.jpg', '13800138002', '<EMAIL>', '0',
        3, '情侣', '深圳', '0', '192.168.1.101', '2025-01-11 11:00:00', '2025-01-02 10:00:00', 8, 1,
        '{"interests":["浪漫","摄影"],"source":"APP"}', 'system', '2025-01-02 10:00:00', '', NULL, '测试用户2'),
       ('U1736604000003', '北海游客003', 'https://example.com/avatar3.jpg', '13800138003', '<EMAIL>', '2',
        1, '休闲', '南宁', '0', '192.168.1.102', '2025-01-11 12:00:00', '2025-01-03 11:00:00', 22, 2,
        '{"interests":["文化","历史"],"source":"网页"}', 'system', '2025-01-03 11:00:00', '', NULL, '测试用户3');

-- 插入用户行为测试数据
INSERT INTO `user_behavior`
VALUES (1, 'U1736604000001', 'search', '{"keyword":"银滩","results":5}', 1, 'session_001', '192.168.1.100',
        'Mozilla/5.0', '2025-01-11 10:05:00', 30, 5, 'system', '2025-01-11 10:05:00', '', NULL, ''),
       (2, 'U1736604000001', 'click', '{"knowledge_id":1,"title":"银滩介绍"}', 1, 'session_001', '192.168.1.100',
        'Mozilla/5.0', '2025-01-11 10:06:00', 120, 4, 'system', '2025-01-11 10:06:00', '', NULL, ''),
       (3, 'U1736604000002', 'chat', '{"message":"涠洲岛怎么去？","response":"需要在北海国际客运港乘船..."}', 9,
        'session_002', '192.168.1.101', 'Mozilla/5.0', '2025-01-11 11:05:00', 60, 5, 'system', '2025-01-11 11:05:00',
        '', NULL, ''),
       (4, 'U1736604000003', 'feedback', '{"type":"like","message":"回答很详细"}', 20, 'session_003', '192.168.1.102',
        'Mozilla/5.0', '2025-01-11 12:05:00', 10, 5, 'system', '2025-01-11 12:05:00', '', NULL, '');

-- 插入用户反馈测试数据
INSERT INTO `user_feedback`
VALUES (1, 'U1736604000001', 'session_001', 1, 'like', '回答很有帮助', 5, '准确,详细', '1', 'admin',
        '2025-01-11 15:00:00', '感谢反馈', 'system', '2025-01-11 10:10:00', 'admin', '2025-01-11 15:00:00', ''),
       (2, 'U1736604000002', 'session_002', 2, 'suggestion', '希望能提供更多交通方式', 4, '建议,交通', '0', NULL, NULL,
        NULL, 'system', '2025-01-11 11:10:00', '', NULL, ''),
       (3, 'U1736604000003', 'session_003', 3, 'like', '信息很全面', 5, '全面,满意', '1', 'admin',
        '2025-01-11 16:00:00', '继续保持', 'system', '2025-01-11 12:10:00', 'admin', '2025-01-11 16:00:00', '');

-- 插入用户偏好测试数据
INSERT INTO `user_preference`
VALUES (1, 'U1736604000001', '{"景点":0.8,"美食":0.9,"住宿":0.6}', '{"银滩":5,"海鲜":4,"亲子":3}',
        '["银滩","海鲜","亲子酒店"]', '[1,5,20]', '{"avg_session_time":300,"preferred_time":"evening"}',
        '2025-01-11 10:30:00', 'system', '2025-01-01 09:00:00', 'system', '2025-01-11 10:30:00', ''),
       (2, 'U1736604000002', '{"景点":0.9,"美食":0.7,"住宿":0.8}', '{"涠洲岛":5,"浪漫":4,"摄影":3}',
        '["涠洲岛","情侣游","海景酒店"]', '[9,26,27]', '{"avg_session_time":450,"preferred_time":"afternoon"}',
        '2025-01-11 11:30:00', 'system', '2025-01-02 10:00:00', 'system', '2025-01-11 11:30:00', ''),
       (3, 'U1736604000003', '{"景点":0.7,"美食":0.6,"住宿":0.5}', '{"老街":5,"文化":4,"历史":3}',
        '["老街","文化体验","经济住宿"]', '[6,7,28]', '{"avg_session_time":600,"preferred_time":"morning"}',
        '2025-01-11 12:30:00', 'system', '2025-01-03 11:00:00', 'system', '2025-01-11 12:30:00', '');

-- 创建索引优化查询性能
CREATE INDEX idx_user_behavior_composite ON user_behavior (user_id, behavior_type, behavior_time);
CREATE INDEX idx_user_feedback_composite ON user_feedback (user_id, feedback_type, create_time);
CREATE INDEX idx_chat_user_active ON chat_user (status, login_date);
CREATE INDEX idx_chat_user_register ON chat_user (status, register_time);

-- 提交事务
COMMIT;
