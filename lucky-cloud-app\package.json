{"name": "rexma-temp-vue3", "version": "0.0.0", "scripts": {"start": "rexma start-rexma", "build": "rexma build-rexma", "start:platform": "rexma start", "build:platform": "rexma build"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4000720240327002", "@dcloudio/uni-app-plus": "3.0.0-4000720240327002", "@dcloudio/uni-components": "3.0.0-4000720240327002", "@dcloudio/uni-h5": "3.0.0-4000720240327002", "@dcloudio/uni-mp-alipay": "3.0.0-4000720240327002", "@dcloudio/uni-mp-baidu": "3.0.0-4000720240327002", "@dcloudio/uni-mp-jd": "3.0.0-4000720240327002", "@dcloudio/uni-mp-kuaishou": "3.0.0-4000720240327002", "@dcloudio/uni-mp-lark": "3.0.0-4000720240327002", "@dcloudio/uni-mp-qq": "3.0.0-4000720240327002", "@dcloudio/uni-mp-toutiao": "3.0.0-4000720240327002", "@dcloudio/uni-mp-weixin": "3.0.0-4000720240327002", "@dcloudio/uni-mp-xhs": "3.0.0-4000720240327002", "@dcloudio/uni-quickapp-webview": "3.0.0-4000720240327002", "pinia": "2.0.36", "vue": "^3.3.11", "vue-i18n": "^9.1.9", "wot-design-uni": "^1.3.2", "xinhua-sdk": "^1.16.5"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "3.0.0-4000720240327002", "@dcloudio/uni-cli-shared": "3.0.0-4000720240327002", "@dcloudio/uni-stacktracey": "3.0.0-4000720240327002", "@dcloudio/vite-plugin-uni": "3.0.0-4000720240327002", "@types/node": "^20.14.5", "@vitejs/plugin-legacy": "4.1.1", "@vue/runtime-core": "^3.3.11", "@vue/tsconfig": "^0.1.3", "rexma-cli": "^1.9.3", "sass": "^1.77.6", "typescript": "^4.9.4", "unplugin-auto-import": "^0.17.6", "vite": "4.3.5", "vue-tsc": "^1.0.24"}}