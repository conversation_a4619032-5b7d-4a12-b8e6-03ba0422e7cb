-- H5端用户管理相关表结构

-- 1. H5用户表
DROP TABLE IF EXISTS `h5_user`;
CREATE TABLE `h5_user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone_number` varchar(11) NOT NULL COMMENT '手机号',
  `password` varchar(100) DEFAULT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '用户昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` char(1) DEFAULT '2' COMMENT '性别（0男 1女 2未知）',
  `age` int(3) DEFAULT NULL COMMENT '年龄',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `status` char(1) DEFAULT '0' COMMENT '用户状态（0正常 1停用）',
  `is_guest` char(1) DEFAULT '0' COMMENT '是否游客（0否 1是）',
  `device_id` varchar(100) DEFAULT NULL COMMENT '设备ID',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(128) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int(11) DEFAULT '0' COMMENT '登录次数',
  `register_source` varchar(50) DEFAULT 'h5' COMMENT '注册来源',
  `invite_code` varchar(20) DEFAULT NULL COMMENT '邀请码',
  `inviter_id` bigint(20) DEFAULT NULL COMMENT '邀请人ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_phone_number` (`phone_number`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_invite_code` (`invite_code`),
  KEY `idx_inviter_id` (`inviter_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='H5端用户表';

-- 2. H5用户登录日志表
DROP TABLE IF EXISTS `h5_user_login_log`;
CREATE TABLE `h5_user_login_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `phone_number` varchar(11) DEFAULT NULL COMMENT '手机号',
  `login_type` varchar(20) DEFAULT NULL COMMENT '登录类型（password密码登录 code验证码登录 guest游客登录）',
  `device_id` varchar(100) DEFAULT NULL COMMENT '设备ID',
  `login_ip` varchar(128) DEFAULT NULL COMMENT '登录IP',
  `login_location` varchar(255) DEFAULT NULL COMMENT '登录地点',
  `browser` varchar(50) DEFAULT NULL COMMENT '浏览器类型',
  `os` varchar(50) DEFAULT NULL COMMENT '操作系统',
  `status` char(1) DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) DEFAULT NULL COMMENT '提示消息',
  `login_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_login_time` (`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='H5用户登录日志表';

-- 3. H5验证码记录表
DROP TABLE IF EXISTS `h5_verification_code`;
CREATE TABLE `h5_verification_code` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `phone_number` varchar(11) NOT NULL COMMENT '手机号',
  `code` varchar(10) NOT NULL COMMENT '验证码',
  `code_type` varchar(20) NOT NULL COMMENT '验证码类型（register注册 login登录 reset重置密码）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0未使用 1已使用 2已过期）',
  `send_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
  `use_time` datetime DEFAULT NULL COMMENT '使用时间',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `ip_address` varchar(128) DEFAULT NULL COMMENT 'IP地址',
  `device_id` varchar(100) DEFAULT NULL COMMENT '设备ID',
  PRIMARY KEY (`id`),
  KEY `idx_phone_code_type` (`phone_number`, `code_type`),
  KEY `idx_send_time` (`send_time`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='H5验证码记录表';

-- 4. H5用户会话关联表
DROP TABLE IF EXISTS `h5_user_session`;
CREATE TABLE `h5_user_session` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID（游客为NULL）',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `device_id` varchar(100) DEFAULT NULL COMMENT '设备ID',
  `user_type` char(1) DEFAULT '0' COMMENT '用户类型（0注册用户 1游客）',
  `nickname` varchar(50) DEFAULT NULL COMMENT '用户昵称',
  `ip_address` varchar(128) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_active_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活跃时间',
  `status` char(1) DEFAULT '1' COMMENT '状态（0结束 1活跃）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='H5用户会话关联表';

-- 5. H5消息反馈表
DROP TABLE IF EXISTS `h5_message_feedback`;
CREATE TABLE `h5_message_feedback` (
  `feedback_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '反馈ID',
  `message_id` bigint(20) NOT NULL COMMENT '消息ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `feedback_type` int(1) NOT NULL COMMENT '反馈类型（1有用 0无用）',
  `feedback_content` varchar(500) DEFAULT NULL COMMENT '反馈内容',
  `ip_address` varchar(128) DEFAULT NULL COMMENT 'IP地址',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`feedback_id`),
  KEY `idx_message_id` (`message_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='H5消息反馈表';

-- 6. H5文件上传记录表
DROP TABLE IF EXISTS `h5_file_upload`;
CREATE TABLE `h5_file_upload` (
  `file_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_name` varchar(255) NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型',
  `file_extension` varchar(10) NOT NULL COMMENT '文件扩展名',
  `upload_type` varchar(20) DEFAULT 'chat' COMMENT '上传类型（avatar头像 chat聊天 image图片）',
  `status` char(1) DEFAULT '1' COMMENT '状态（0删除 1正常）',
  `ip_address` varchar(128) DEFAULT NULL COMMENT 'IP地址',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`file_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_upload_type` (`upload_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='H5文件上传记录表';

-- 插入测试数据

-- 插入测试用户
INSERT INTO `h5_user` (`phone_number`, `password`, `nickname`, `gender`, `age`, `city`, `status`, `is_guest`, `register_source`) VALUES
('13800138001', '$2a$10$7JB720yubVSOfvVWmGOi4OvuDDEsHoUyoSfxX6M.UjWPiRANy6.0O', '测试用户1', '1', 25, '北京', '0', '0', 'h5'),
('13800138002', '$2a$10$7JB720yubVSOfvVWmGOi4OvuDDEsHoUyoSfxX6M.UjWPiRANy6.0O', '测试用户2', '0', 30, '上海', '0', '0', 'h5'),
('13800138003', '$2a$10$7JB720yubVSOfvVWmGOi4OvuDDEsHoUyoSfxX6M.UjWPiRANy6.0O', '测试用户3', '2', 28, '广州', '0', '0', 'h5');

-- 插入测试登录日志
INSERT INTO `h5_user_login_log` (`user_id`, `phone_number`, `login_type`, `device_id`, `login_ip`, `browser`, `os`, `status`, `msg`) VALUES
(1, '13800138001', 'password', 'device001', '*************', 'Chrome', 'Windows', '0', '登录成功'),
(2, '13800138002', 'code', 'device002', '*************', 'Safari', 'iOS', '0', '登录成功'),
(3, '13800138003', 'password', 'device003', '*************', 'Firefox', 'Android', '0', '登录成功');

-- 创建索引优化查询性能
CREATE INDEX idx_h5_user_phone_status ON h5_user(phone_number, status);
CREATE INDEX idx_h5_user_login_log_user_time ON h5_user_login_log(user_id, login_time);
CREATE INDEX idx_h5_verification_code_phone_type_status ON h5_verification_code(phone_number, code_type, status);
CREATE INDEX idx_h5_user_session_user_status ON h5_user_session(user_id, status);
CREATE INDEX idx_h5_message_feedback_message_type ON h5_message_feedback(message_id, feedback_type);
CREATE INDEX idx_h5_file_upload_user_type_status ON h5_file_upload(user_id, upload_type, status);
