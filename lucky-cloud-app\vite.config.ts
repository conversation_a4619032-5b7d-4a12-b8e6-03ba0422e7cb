import { defineConfig } from 'vite';
import uni from '@dcloudio/vite-plugin-uni';

import AutoImport from 'unplugin-auto-import/vite';

import legacy from '@vitejs/plugin-legacy';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni(),
    AutoImport({
      imports: [
        'vue',
        'uni-app',
        {
          'rexma-cli': ['xma'],
        },
      ],
      dts: 'src/declaration/auto-imports.d.ts',
    }),
    process.env.UNI_PLATFORM === 'h5' &&
      legacy({
        targets: ['defaults', 'not IE 11'],
      }),
  ].filter(Boolean),

  server: {
    open: true,
    // 代理配置
    // proxy: {
    //   '/api': {
    //     target: 'https://www.api.com',
    //     changeOrigin: true,
    //   },
    // },
  },
});
