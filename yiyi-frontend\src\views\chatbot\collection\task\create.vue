<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>基本信息</span>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="form.taskName" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务类型" prop="taskType">
              <el-select v-model="form.taskType" placeholder="请选择任务类型">
                <el-option label="定时任务" value="SCHEDULED" />
                <el-option label="手动任务" value="MANUAL" />
                <el-option label="触发任务" value="TRIGGER" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="数据源类型" prop="sourceType">
              <el-select v-model="form.sourceType" placeholder="请选择数据源类型" @change="handleSourceTypeChange">
                <el-option label="Web爬虫" value="WEB_CRAWLER" />
                <el-option label="API接口" value="API" />
                <el-option label="数据库" value="DATABASE" />
                <el-option label="文件" value="FILE" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="执行频率" prop="cronExpression" v-if="form.taskType === 'SCHEDULED'">
              <el-input v-model="form.cronExpression" placeholder="请输入Cron表达式" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="任务描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入任务描述" />
        </el-form-item>
      </el-card>

      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>数据源配置</span>
        </div>
        
        <!-- Web爬虫配置 -->
        <div v-if="form.sourceType === 'WEB_CRAWLER'">
          <el-form-item label="目标URL" prop="sourceConfig.url">
            <el-input v-model="form.sourceConfig.url" placeholder="请输入要爬取的网页URL" />
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item label="爬取深度" prop="sourceConfig.depth">
                <el-input-number v-model="form.sourceConfig.depth" :min="1" :max="10" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="请求间隔(秒)" prop="sourceConfig.delay">
                <el-input-number v-model="form.sourceConfig.delay" :min="1" :max="60" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="CSS选择器" prop="sourceConfig.selector">
            <el-input v-model="form.sourceConfig.selector" placeholder="请输入CSS选择器" />
          </el-form-item>
        </div>

        <!-- API接口配置 -->
        <div v-if="form.sourceType === 'API'">
          <el-form-item label="API地址" prop="sourceConfig.apiUrl">
            <el-input v-model="form.sourceConfig.apiUrl" placeholder="请输入API接口地址" />
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item label="请求方法" prop="sourceConfig.method">
                <el-select v-model="form.sourceConfig.method" placeholder="请选择请求方法">
                  <el-option label="GET" value="GET" />
                  <el-option label="POST" value="POST" />
                  <el-option label="PUT" value="PUT" />
                  <el-option label="DELETE" value="DELETE" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="超时时间(秒)" prop="sourceConfig.timeout">
                <el-input-number v-model="form.sourceConfig.timeout" :min="5" :max="300" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="请求头" prop="sourceConfig.headers">
            <el-input v-model="form.sourceConfig.headers" type="textarea" placeholder="请输入请求头(JSON格式)" />
          </el-form-item>
          <el-form-item label="请求参数" prop="sourceConfig.params">
            <el-input v-model="form.sourceConfig.params" type="textarea" placeholder="请输入请求参数(JSON格式)" />
          </el-form-item>
        </div>

        <!-- 数据库配置 -->
        <div v-if="form.sourceType === 'DATABASE'">
          <el-row>
            <el-col :span="12">
              <el-form-item label="数据库类型" prop="sourceConfig.dbType">
                <el-select v-model="form.sourceConfig.dbType" placeholder="请选择数据库类型">
                  <el-option label="MySQL" value="mysql" />
                  <el-option label="PostgreSQL" value="postgresql" />
                  <el-option label="Oracle" value="oracle" />
                  <el-option label="SQL Server" value="sqlserver" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="端口" prop="sourceConfig.port">
                <el-input-number v-model="form.sourceConfig.port" :min="1" :max="65535" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="主机地址" prop="sourceConfig.host">
                <el-input v-model="form.sourceConfig.host" placeholder="请输入数据库主机地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据库名" prop="sourceConfig.database">
                <el-input v-model="form.sourceConfig.database" placeholder="请输入数据库名" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="用户名" prop="sourceConfig.username">
                <el-input v-model="form.sourceConfig.username" placeholder="请输入用户名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码" prop="sourceConfig.password">
                <el-input v-model="form.sourceConfig.password" type="password" placeholder="请输入密码" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="SQL查询" prop="sourceConfig.sql">
            <el-input v-model="form.sourceConfig.sql" type="textarea" placeholder="请输入SQL查询语句" />
          </el-form-item>
        </div>

        <!-- 文件配置 -->
        <div v-if="form.sourceType === 'FILE'">
          <el-form-item label="文件路径" prop="sourceConfig.filePath">
            <el-input v-model="form.sourceConfig.filePath" placeholder="请输入文件路径" />
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item label="文件类型" prop="sourceConfig.fileType">
                <el-select v-model="form.sourceConfig.fileType" placeholder="请选择文件类型">
                  <el-option label="CSV" value="csv" />
                  <el-option label="Excel" value="excel" />
                  <el-option label="JSON" value="json" />
                  <el-option label="XML" value="xml" />
                  <el-option label="TXT" value="txt" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="编码格式" prop="sourceConfig.encoding">
                <el-select v-model="form.sourceConfig.encoding" placeholder="请选择编码格式">
                  <el-option label="UTF-8" value="utf-8" />
                  <el-option label="GBK" value="gbk" />
                  <el-option label="GB2312" value="gb2312" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>数据处理配置</span>
        </div>
        <el-form-item label="数据清洗规则" prop="cleaningRules">
          <el-input v-model="form.cleaningRules" type="textarea" placeholder="请输入数据清洗规则(JSON格式)" />
        </el-form-item>
        <el-form-item label="数据转换规则" prop="transformRules">
          <el-input v-model="form.transformRules" type="textarea" placeholder="请输入数据转换规则(JSON格式)" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="批处理大小" prop="batchSize">
              <el-input-number v-model="form.batchSize" :min="1" :max="10000" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大重试次数" prop="maxRetries">
              <el-input-number v-model="form.maxRetries" :min="0" :max="10" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <div style="margin-top: 20px; text-align: center;">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
        <el-button type="info" @click="testConnection" v-if="form.sourceType">测试连接</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { addTask, testConnection } from "@/api/chatbot/collection/task";

export default {
  name: "CreateCollectionTask",
  data() {
    return {
      // 表单参数
      form: {
        taskName: null,
        taskType: 'SCHEDULED',
        sourceType: null,
        cronExpression: '0 0 2 * * ?',
        description: null,
        sourceConfig: {},
        cleaningRules: null,
        transformRules: null,
        batchSize: 100,
        maxRetries: 3
      },
      // 表单校验
      rules: {
        taskName: [
          { required: true, message: "任务名称不能为空", trigger: "blur" }
        ],
        taskType: [
          { required: true, message: "任务类型不能为空", trigger: "change" }
        ],
        sourceType: [
          { required: true, message: "数据源类型不能为空", trigger: "change" }
        ]
      }
    };
  },
  methods: {
    /** 数据源类型改变 */
    handleSourceTypeChange(value) {
      this.form.sourceConfig = {};
      // 根据不同类型设置默认值
      if (value === 'WEB_CRAWLER') {
        this.form.sourceConfig = {
          url: '',
          depth: 1,
          delay: 2,
          selector: ''
        };
      } else if (value === 'API') {
        this.form.sourceConfig = {
          apiUrl: '',
          method: 'GET',
          timeout: 30,
          headers: '{}',
          params: '{}'
        };
      } else if (value === 'DATABASE') {
        this.form.sourceConfig = {
          dbType: 'mysql',
          host: 'localhost',
          port: 3306,
          database: '',
          username: '',
          password: '',
          sql: ''
        };
      } else if (value === 'FILE') {
        this.form.sourceConfig = {
          filePath: '',
          fileType: 'csv',
          encoding: 'utf-8'
        };
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          addTask(this.form).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.$router.push("/chatbot/collection/task");
          });
        }
      });
    },
    /** 取消按钮 */
    cancel() {
      this.$router.push("/chatbot/collection/task");
    },
    /** 测试连接 */
    testConnection() {
      if (!this.form.sourceType) {
        this.$modal.msgWarning("请先选择数据源类型");
        return;
      }
      
      const testData = {
        sourceType: this.form.sourceType,
        sourceConfig: this.form.sourceConfig
      };
      
      testConnection(testData).then(response => {
        this.$modal.msgSuccess("连接测试成功");
      }).catch(() => {
        this.$modal.msgError("连接测试失败");
      });
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
</style>
