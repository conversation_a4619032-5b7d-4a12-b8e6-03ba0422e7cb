/**
 * 用户管理模块路由配置
 * 包含用户列表、用户详情、用户统计等功能路由
 */

// 用户管理相关路由配置
const userRoutes = [
  {
    path: 'user/list',
    component: () => import('@/views/user/list/index.vue'),
    name: 'UserList',
    meta: { 
      title: '用户列表', 
      icon: 'user',
      keepAlive: true
    }
  },
  {
    path: 'user/detail/:id',
    component: () => import('@/views/user/detail/index.vue'),
    name: 'UserDetail',
    meta: { 
      title: '用户详情', 
      hidden: true,
      activeMenu: '/chatbot/user/list'
    }
  },
  {
    path: 'user/statistics',
    component: () => import('@/views/user/statistics/index.vue'),
    name: 'UserStatistics',
    meta: { 
      title: '用户统计', 
      icon: 'data-analysis',
      keepAlive: true
    }
  }
]

export default userRoutes
