/**
 * H5端WebSocket管理类
 * 用于处理实时聊天通信
 */

export interface WebSocketMessage {
    type: 'message' | 'heartbeat' | 'typing' | 'chatResponse' | 'error'
    content?: string
    messageType?: string
    senderName?: string
    sessionId?: string
    status?: string
    userMessage?: any
    aiMessage?: any
    relatedQuestions?: string[]
    timestamp?: number
    message?: string
}

export interface WebSocketConfig {
    url: string
    heartbeatInterval?: number
    reconnectInterval?: number
    maxReconnectAttempts?: number
    debug?: boolean
}

export type WebSocketEventHandler = (message: WebSocketMessage) => void

export class H5WebSocketManager {
    private ws: WebSocket | null = null
    private config: Required<WebSocketConfig>
    private heartbeatTimer: number | null = null
    private reconnectTimer: number | null = null
    private reconnectAttempts = 0
    private isConnecting = false
    private isManualClose = false
    
    // 事件处理器
    private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map()

    constructor(config: WebSocketConfig) {
        this.config = {
            heartbeatInterval: 30000, // 30秒心跳
            reconnectInterval: 5000,  // 5秒重连间隔
            maxReconnectAttempts: 5,  // 最大重连次数
            debug: false,
            ...config
        }
    }

    /**
     * 连接WebSocket
     */
    connect(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                resolve()
                return
            }

            if (this.isConnecting) {
                reject(new Error('WebSocket is connecting'))
                return
            }

            this.isConnecting = true
            this.isManualClose = false

            try {
                this.ws = new WebSocket(this.config.url)

                this.ws.onopen = () => {
                    this.isConnecting = false
                    this.reconnectAttempts = 0
                    this.startHeartbeat()
                    this.log('WebSocket连接成功')
                    this.emit('open', { type: 'message', content: '连接成功' })
                    resolve()
                }

                this.ws.onmessage = (event) => {
                    try {
                        const message: WebSocketMessage = JSON.parse(event.data)
                        this.handleMessage(message)
                    } catch (error) {
                        this.log('解析消息失败:', error)
                        this.emit('error', { 
                            type: 'error', 
                            message: '消息格式错误' 
                        })
                    }
                }

                this.ws.onclose = (event) => {
                    this.isConnecting = false
                    this.stopHeartbeat()
                    this.log(`WebSocket连接关闭 (代码: ${event.code})`)
                    this.emit('close', { 
                        type: 'message', 
                        content: `连接已断开 (${event.code})` 
                    })

                    if (!this.isManualClose && this.reconnectAttempts < this.config.maxReconnectAttempts) {
                        this.scheduleReconnect()
                    }
                }

                this.ws.onerror = (error) => {
                    this.isConnecting = false
                    this.log('WebSocket错误:', error)
                    this.emit('error', { 
                        type: 'error', 
                        message: 'WebSocket连接错误' 
                    })
                    reject(error)
                }

            } catch (error) {
                this.isConnecting = false
                reject(error)
            }
        })
    }

    /**
     * 断开连接
     */
    disconnect(): void {
        this.isManualClose = true
        this.stopHeartbeat()
        this.clearReconnectTimer()

        if (this.ws) {
            this.ws.close()
            this.ws = null
        }
    }

    /**
     * 发送消息
     */
    send(message: WebSocketMessage): boolean {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            this.log('WebSocket未连接，无法发送消息')
            return false
        }

        try {
            this.ws.send(JSON.stringify(message))
            this.log('发送消息:', message)
            return true
        } catch (error) {
            this.log('发送消息失败:', error)
            return false
        }
    }

    /**
     * 发送聊天消息
     */
    sendChatMessage(content: string, senderName?: string): boolean {
        return this.send({
            type: 'message',
            content,
            messageType: '1',
            senderName
        })
    }

    /**
     * 发送心跳
     */
    sendHeartbeat(): boolean {
        return this.send({
            type: 'heartbeat'
        })
    }

    /**
     * 添加事件监听器
     */
    on(event: string, handler: WebSocketEventHandler): void {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, [])
        }
        this.eventHandlers.get(event)!.push(handler)
    }

    /**
     * 获取连接状态
     */
    getReadyState(): number {
        return this.ws ? this.ws.readyState : WebSocket.CLOSED
    }

    /**
     * 是否已连接
     */
    isConnected(): boolean {
        return this.ws !== null && this.ws.readyState === WebSocket.OPEN
    }

    /**
     * 处理接收到的消息
     */
    private handleMessage(message: WebSocketMessage): void {
        this.log('收到消息:', message)

        switch (message.type) {
            case 'heartbeat':
                this.emit('heartbeat', message)
                break
            case 'chatResponse':
                this.emit('chatResponse', message)
                break
            case 'error':
                this.emit('error', message)
                break
            default:
                this.emit('message', message)
        }
    }

    /**
     * 触发事件
     */
    private emit(event: string, message: WebSocketMessage): void {
        const handlers = this.eventHandlers.get(event)
        if (handlers) {
            handlers.forEach(handler => {
                try {
                    handler(message)
                } catch (error) {
                    this.log('事件处理器执行失败:', error)
                }
            })
        }
    }

    /**
     * 开始心跳
     */
    private startHeartbeat(): void {
        this.stopHeartbeat()
        this.heartbeatTimer = window.setInterval(() => {
            if (this.isConnected()) {
                this.sendHeartbeat()
            }
        }, this.config.heartbeatInterval)
    }

    /**
     * 停止心跳
     */
    private stopHeartbeat(): void {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer)
            this.heartbeatTimer = null
        }
    }

    /**
     * 安排重连
     */
    private scheduleReconnect(): void {
        this.clearReconnectTimer()
        this.reconnectAttempts++
        
        this.log(`准备第${this.reconnectAttempts}次重连...`)
        
        this.reconnectTimer = window.setTimeout(() => {
            this.log(`开始第${this.reconnectAttempts}次重连`)
            this.connect().catch(error => {
                this.log('重连失败:', error)
            })
        }, this.config.reconnectInterval)
    }

    /**
     * 清除重连定时器
     */
    private clearReconnectTimer(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer)
            this.reconnectTimer = null
        }
    }

    /**
     * 日志输出
     */
    private log(...args: any[]): void {
        if (this.config.debug) {
            console.log('[H5WebSocket]', ...args)
        }
    }

    /**
     * 销毁实例
     */
    destroy(): void {
        this.disconnect()
        this.eventHandlers.clear()
    }
}
