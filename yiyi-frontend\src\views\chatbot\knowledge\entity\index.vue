<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="实体名称" prop="entityName">
        <el-input
          v-model="queryParams.entityName"
          placeholder="请输入实体名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="实体类型" prop="entityType">
        <el-select v-model="queryParams.entityType" placeholder="请选择实体类型" clearable>
          <el-option label="景点" value="ATTRACTION" />
          <el-option label="美食" value="FOOD" />
          <el-option label="酒店" value="HOTEL" />
          <el-option label="交通" value="TRANSPORT" />
        </el-select>
      </el-form-item>
      <el-form-item label="地理位置" prop="location">
        <el-input
          v-model="queryParams.location"
          placeholder="请输入地理位置"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="实体状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['chatbot:knowledge:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['chatbot:knowledge:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['chatbot:knowledge:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['chatbot:knowledge:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="entityList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="实体ID" align="center" prop="entityId" />
      <el-table-column label="实体名称" align="center" prop="entityName" :show-overflow-tooltip="true" />
      <el-table-column label="实体类型" align="center" prop="entityType">
        <template slot-scope="scope">
          <dict-tag :options="entityTypeOptions" :value="scope.row.entityType"/>
        </template>
      </el-table-column>
      <el-table-column label="描述" align="center" prop="description" :show-overflow-tooltip="true" />
      <el-table-column label="地理位置" align="center" prop="location" :show-overflow-tooltip="true" />
      <el-table-column label="热度评分" align="center" prop="popularity">
        <template slot-scope="scope">
          <el-rate
            v-model="scope.row.popularity"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['chatbot:knowledge:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['chatbot:knowledge:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['chatbot:knowledge:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改知识实体对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="实体名称" prop="entityName">
              <el-input v-model="form.entityName" placeholder="请输入实体名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实体类型" prop="entityType">
              <el-select v-model="form.entityType" placeholder="请选择实体类型">
                <el-option label="景点" value="ATTRACTION" />
                <el-option label="美食" value="FOOD" />
                <el-option label="酒店" value="HOTEL" />
                <el-option label="交通" value="TRANSPORT" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="地理位置" prop="location">
              <el-input v-model="form.location" placeholder="请输入地理位置" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="经度" prop="longitude">
              <el-input-number v-model="form.longitude" :precision="7" placeholder="经度" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number v-model="form.latitude" :precision="7" placeholder="纬度" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="标签" prop="tags">
              <el-input v-model="form.tags" placeholder="请输入标签，逗号分隔" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="热度评分" prop="popularity">
              <el-input-number v-model="form.popularity" :min="0" :max="5" :precision="2" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 实体详情对话框 -->
    <el-dialog title="实体详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="实体名称">{{ detailForm.entityName }}</el-descriptions-item>
        <el-descriptions-item label="实体类型">{{ detailForm.entityType }}</el-descriptions-item>
        <el-descriptions-item label="地理位置">{{ detailForm.location }}</el-descriptions-item>
        <el-descriptions-item label="热度评分">{{ detailForm.popularity }}</el-descriptions-item>
        <el-descriptions-item label="经纬度">{{ detailForm.longitude }}, {{ detailForm.latitude }}</el-descriptions-item>
        <el-descriptions-item label="标签">{{ detailForm.tags }}</el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ detailForm.createTime }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ detailForm.description }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { listEntity, getEntity, delEntity, addEntity, updateEntity } from "@/api/chatbot/knowledge/entity";

export default {
  name: "Entity",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 知识实体表格数据
      entityList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 详情弹出层
      detailOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entityName: null,
        entityType: null,
        location: null,
        status: null
      },
      // 表单参数
      form: {},
      // 详情表单
      detailForm: {},
      // 实体类型选项
      entityTypeOptions: [
        { label: "景点", value: "ATTRACTION" },
        { label: "美食", value: "FOOD" },
        { label: "酒店", value: "HOTEL" },
        { label: "交通", value: "TRANSPORT" }
      ],
      // 表单校验
      rules: {
        entityName: [
          { required: true, message: "实体名称不能为空", trigger: "blur" }
        ],
        entityType: [
          { required: true, message: "实体类型不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询知识实体列表 */
    getList() {
      this.loading = true;
      listEntity(this.queryParams).then(response => {
        this.entityList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        entityId: null,
        entityName: null,
        entityType: null,
        description: null,
        location: null,
        longitude: null,
        latitude: null,
        tags: null,
        popularity: 0,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.entityId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加知识实体";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const entityId = row.entityId || this.ids
      getEntity(entityId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改知识实体";
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      getEntity(row.entityId).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.entityId != null) {
            updateEntity(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEntity(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const entityIds = row.entityId || this.ids;
      this.$modal.confirm('是否确认删除知识实体编号为"' + entityIds + '"的数据项？').then(function() {
        return delEntity(entityIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('chatbot/knowledge/entity/export', {
        ...this.queryParams
      }, `entity_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
