/**
 * 行为分析模块路由配置
 * 包含行为监控、行为统计等功能路由
 */

// 行为分析相关路由配置
const behaviorRoutes = [
  {
    path: 'behavior/monitor',
    component: () => import('@/views/behavior/monitor/index.vue'),
    name: 'BehaviorMonitor',
    meta: {
      title: '行为监控',
      icon: 'monitor',
      keepAlive: true
    }
  },
  {
    path: 'behavior/statistics',
    component: () => import('@/views/behavior/statistics/index.vue'),
    name: 'BehaviorStatistics',
    meta: {
      title: '行为统计',
      icon: 'data-analysis',
      keepAlive: true
    }
  }
]

export default behaviorRoutes
